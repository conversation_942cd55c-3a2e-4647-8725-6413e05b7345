!function(e){var t={};function n(a){if(t[a])return t[a].exports;var r=t[a]={i:a,l:!1,exports:{}};return e[a].call(r.exports,r,r.exports,n),r.l=!0,r.exports}n.m=e,n.c=t,n.d=function(e,t,a){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:a})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var a=Object.create(null);if(n.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(a,r,function(t){return e[t]}.bind(null,r));return a},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=1)}([,function(e,t){const n=window._ckyGcm;let a=!0;const r=n.default_settings||[],o=n.wait_for_update,i=window.ckySettings&&window.ckySettings.dataLayerName?window.ckySettings.dataLayerName:"dataLayer";function d(){window[i].push(arguments)}function s(e){o>0&&(e.wait_for_update=o),d("consent","default",e)}window[i]=window[i]||[],d("set","ads_data_redaction",!!n.ads_data_redaction),d("set","url_passthrough",!!n.url_passthrough),d("set","developer_id.dY2Q2ZW",!0);for(let e=0;e<r.length;e++){const t=r[e],n={ad_storage:t.advertisement,analytics_storage:t.analytics,functionality_storage:t.functional,personalization_storage:t.functional,security_storage:t.necessary,ad_user_data:t.ad_user_data,ad_personalization:t.ad_personalization},o=t.regions.split(",").map(e=>e.trim()).filter(e=>e);o.length>0&&"all"!==o[0].toLowerCase()?n.region=o:a=!1,s(n)}a&&s({ad_storage:"denied",analytics_storage:"denied",functionality_storage:"denied",personalization_storage:"denied",security_storage:"granted",ad_user_data:"denied",ad_personalization:"denied"})}]);