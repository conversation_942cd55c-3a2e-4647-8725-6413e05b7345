{"name": "gdpr-cookie-consent", "version": "3.0.6", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build --mode development --watch", "build:production": "vue-cli-service build && yarn run build-webpack-lite && node build.js --mode production", "lint": "vue-cli-service lint", "build-webpack-lite": "webpack-cli --config webpack/lite.config.js --mode production"}, "dependencies": {"@popperjs/core": "^2.11.5", "@wordpress/api-fetch": "^5.2.1", "apexcharts": "^3.44.2", "chart.js": "^3.9.1", "core-js": "^3.6.5", "md5": "^2.3.0", "moment": "^2.30.1", "sass": "^1.62.1", "vue": "^2.7.16", "vue-apexcharts": "1.6.1", "vue-color": "^2.8.1", "vue-frag": "^1.4.1", "vue-router": "^3.2.0", "vuex": "^3.6.2"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/preset-env": "^7.26.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "sass-loader": "^10", "vue-template-compiler": "^2.7.16", "webpack-cli": "^4.10.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}