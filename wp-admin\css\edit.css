#poststuff {
	padding-top: 10px;
	min-width: 763px;
}

#poststuff #post-body {
	padding: 0;
}

#poststuff .postbox-container {
	width: 100%;
}

#poststuff #post-body.columns-2 {
	margin-right: 300px;
}

/*------------------------------------------------------------------------------
  11.0 - Write/Edit Post Screen
------------------------------------------------------------------------------*/

#show-comments {
	overflow: hidden;
}

#save-action .spinner,
#show-comments a {
	float: left;
}

#show-comments .spinner {
	float: none;
	margin-top: 0;
}

#lost-connection-notice .spinner {
	visibility: visible;
	float: left;
	margin: 0 5px 0 0;
}

#titlediv {
	position: relative;
}

#titlediv label {
	cursor: text;
}

#titlediv div.inside {
	margin: 0;
}

#poststuff #titlewrap {
	border: 0;
	padding: 0;
}

#titlediv #title {
	padding: 3px 8px;
	font-size: 1.7em;
	line-height: 100%;
	height: 1.7em;
	width: 100%;
	outline: none;
	margin: 0 0 3px;
	background-color: #fff;
}

#titlediv #title-prompt-text {
	color: #646970;
	position: absolute;
	font-size: 1.7em;
	padding: 10px;
	pointer-events: none;
}

#titlewrap .skiplink:focus {
	clip: inherit;
	clip-path: inherit;
	right: 4px;
	top: 4px;
	width: auto;
}

input#link_description,
input#link_url {
	width: 100%;
}

#pending {
	background: 0 none;
	border: 0 none;
	padding: 0;
	font-size: 11px;
	margin-top: -1px;
}

#edit-slug-box,
#comment-link-box {
	line-height: 1.84615384;
	min-height: 25px;
	margin-top: 5px;
	padding: 0 10px;
	color: #646970;
}

#sample-permalink {
	display: inline-block;
	max-width: 100%;
	word-wrap: break-word;
}

#edit-slug-box .cancel {
	margin-right: 10px;
	padding: 0;
	font-size: 11px;
}

#comment-link-box {
	margin: 5px 0;
	padding: 0 5px;
}

#editable-post-name-full {
	display: none;
}

#editable-post-name {
	font-weight: 600;
}

#editable-post-name input {
	font-size: 13px;
	font-weight: 400;
	height: 24px;
	margin: 0;
	width: 16em;
}

.postarea h3 label {
	float: left;
}

body.post-new-php .submitbox .submitdelete {
	display: none;
}

.submitbox .submit a:hover {
	text-decoration: underline;
}

.submitbox .submit input {
	margin-bottom: 8px;
	margin-right: 4px;
	padding: 6px;
}

#post-status-select {
	margin-top: 3px;
}

body.post-type-wp_navigation div#minor-publishing,
body.post-type-wp_navigation .inline-edit-status {
	display: none;
}

/* Post Screen */

/* Only highlight drop zones when dragging and only in the 2 columns layout. */
.is-dragging-metaboxes .metabox-holder .postbox-container .meta-box-sortables {
	outline: 3px dashed #646970;
	/* Prevent margin on the child from collapsing with margin on the parent. */
	display: flow-root;
	/*
	 * This min-height is meant to limit jumpiness while dragging. It's equivalent
	 * to the minimum height of the sortable-placeholder which is given by the height
	 * of a collapsed post box (36px + 1px top and bottom borders) + the placeholder
	 * bottom margin (20px) + 2 additional pixels to compensate browsers rounding.
	 */
	min-height: 60px;
	margin-bottom: 20px;
}

.postbox {
	position: relative;
	min-width: 255px;
	border: 1px solid #c3c4c7;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
	background: #fff;
}

#trackback_url {
	width: 99%;
}

#normal-sortables .postbox .submit {
	background: transparent none;
	border: 0 none;
	float: right;
	padding: 0 12px;
	margin: 0;
}

.category-add input[type="text"],
.category-add select {
	width: 100%;
	max-width: 260px;
	vertical-align: baseline;
}

#side-sortables .category-add input[type="text"],
#side-sortables .category-add select {
	margin: 0 0 1em;
}

ul.category-tabs li,
#side-sortables .add-menu-item-tabs li,
.wp-tab-bar li {
	display: inline;
	line-height: 1.35;
}

.no-js .category-tabs li.hide-if-no-js {
	display: none;
}

.category-tabs a,
#side-sortables .add-menu-item-tabs a,
.wp-tab-bar a {
	text-decoration: none;
}

/* @todo: do these really need to be so specific? */
#side-sortables .category-tabs .tabs a,
#side-sortables .add-menu-item-tabs .tabs a,
.wp-tab-bar .wp-tab-active a,
#post-body ul.category-tabs li.tabs a,
#post-body ul.add-menu-item-tabs li.tabs a {
	color: #2c3338;
}

.category-tabs {
	margin: 8px 0 5px;
}

/* Back-compat for pre-4.4 */
#category-adder h4 {
	margin: 0;
}

.taxonomy-add-new {
	display: inline-block;
	margin: 10px 0;
	font-weight: 600;
}

#side-sortables .add-menu-item-tabs,
.wp-tab-bar {
	margin-bottom: 3px;
}

#normal-sortables .postbox #replyrow .submit {
	float: none;
	margin: 0;
	padding: 5px 7px 10px;
	overflow: hidden;
}

#side-sortables .submitbox .submit input,
#side-sortables .submitbox .submit .preview,
#side-sortables .submitbox .submit a.preview:hover {
	border: 0 none;
}

/* @todo: make this a more generic class */
ul.category-tabs,
ul.add-menu-item-tabs,
ul.wp-tab-bar {
	margin-top: 12px;
}

ul.category-tabs li,
ul.add-menu-item-tabs li {
	border: solid 1px transparent;
	position: relative;
}

ul.category-tabs li.tabs,
ul.add-menu-item-tabs li.tabs,
.wp-tab-active {
	border: 1px solid #dcdcde;
	border-bottom-color: #fff;
	background-color: #fff;
}

ul.category-tabs li,
ul.add-menu-item-tabs li,
ul.wp-tab-bar li {
	padding: 3px 5px 6px;
}

#set-post-thumbnail {
	display: inline-block;
	max-width: 100%;
}

#postimagediv .inside img {
	max-width: 100%;
	height: auto;
	width: auto;
	vertical-align: top;
	background-image: linear-gradient(45deg, #c3c4c7 25%, transparent 25%, transparent 75%, #c3c4c7 75%, #c3c4c7), linear-gradient(45deg, #c3c4c7 25%, transparent 25%, transparent 75%, #c3c4c7 75%, #c3c4c7);
	background-position: 0 0, 10px 10px;
	background-size: 20px 20px;
}

form#tags-filter {
	position: relative;
}

/* Global classes */
.wp-hidden-children .wp-hidden-child,
.ui-tabs-hide {
	display: none;
}

#post-body .tagsdiv #newtag {
	margin-right: 5px;
	width: 16em;
}

#side-sortables input#post_password {
	width: 94%
}

#side-sortables .tagsdiv #newtag {
	width: 68%;
}

#post-status-info {
	width: 100%;
	border-spacing: 0;
	border: 1px solid #c3c4c7;
	border-top: none;
	background-color: #f6f7f7;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
	z-index: 999;
}

#post-status-info td {
	font-size: 12px;
}

.autosave-info {
	padding: 2px 10px;
	text-align: right;
}

#editorcontent #post-status-info {
	border: none;
}

#content-resize-handle {
	background: transparent url(../images/resize.gif) no-repeat scroll right bottom;
	width: 12px;
	cursor: row-resize;
}

/*rtl:ignore*/
.rtl #content-resize-handle {
	background-image: url(../images/resize-rtl.gif);
	background-position: left bottom;
}

.wp-editor-expand #content-resize-handle {
	display: none;
}

#postdivrich #content {
	resize: none;
}

#wp-word-count {
	padding: 2px 10px;
}

#wp-content-editor-container {
	position: relative;
}

.wp-editor-expand #wp-content-editor-tools {
	z-index: 1000;
	border-bottom: 1px solid #c3c4c7;
}

.wp-editor-expand #wp-content-editor-container {
	box-shadow: none;
	margin-top: -1px;
}

.wp-editor-expand #wp-content-editor-container {
	border-bottom: 0 none;
}

.wp-editor-expand div.mce-statusbar {
	z-index: 1;
}

.wp-editor-expand #post-status-info {
	border-top: 1px solid #c3c4c7;
}

.wp-editor-expand div.mce-toolbar-grp {
	z-index: 999;
}

/* TinyMCE native fullscreen mode override */
.mce-fullscreen #wp-content-wrap .mce-menubar,
.mce-fullscreen #wp-content-wrap .mce-toolbar-grp,
.mce-fullscreen #wp-content-wrap .mce-edit-area,
.mce-fullscreen #wp-content-wrap .mce-statusbar {
	position: static !important;
	width: auto !important;
	padding: 0 !important;
}

.mce-fullscreen #wp-content-wrap .mce-statusbar {
	visibility: visible !important;
}

.mce-fullscreen #wp-content-wrap .mce-tinymce .mce-wp-dfw {
	display: none;
}

.post-php.mce-fullscreen #wpadminbar,
.mce-fullscreen #wp-content-wrap .mce-wp-dfw {
	display: none;
}
/* End TinyMCE native fullscreen mode override */

#wp-content-editor-tools {
	background-color: #f0f0f1;
	padding-top: 20px;
}

#poststuff #post-body.columns-2 #side-sortables {
	width: 280px;
}

#timestampdiv select {
	vertical-align: top;
	font-size: 12px;
	line-height: 2.33333333; /* 28px */
}

#aa, #jj, #hh, #mn {
	padding: 6px 1px;
	font-size: 12px;
	line-height: 1.16666666; /* 14px */
}

#jj, #hh, #mn {
	width: 2em;
}

#aa {
	width: 3.4em;
}

.curtime #timestamp {
	padding: 2px 0 1px;
	display: inline !important;
	height: auto !important;
}

#post-body .misc-pub-post-status:before,
#post-body #visibility:before,
.curtime #timestamp:before,
#post-body .misc-pub-uploadedby:before,
#post-body .misc-pub-uploadedto:before,
#post-body .misc-pub-revisions:before,
#post-body .misc-pub-response-to:before,
#post-body .misc-pub-comment-status:before {
	color: #8c8f94;
}

#post-body .misc-pub-post-status:before,
#post-body #visibility:before,
.curtime #timestamp:before,
#post-body .misc-pub-uploadedby:before,
#post-body .misc-pub-uploadedto:before,
#post-body .misc-pub-revisions:before,
#post-body .misc-pub-response-to:before,
#post-body .misc-pub-comment-status:before {
	font: normal 20px/1 dashicons;
	speak: never;
	display: inline-block;
	margin-left: -1px;
	padding-right: 3px;
	vertical-align: top;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

#post-body .misc-pub-post-status:before,
#post-body .misc-pub-comment-status:before {
	content: "\f173";
}

#post-body #visibility:before {
	content: "\f177";
}

.curtime #timestamp:before {
	content: "\f145";
	position: relative;
	top: -1px;
}

#post-body .misc-pub-uploadedby:before {
	content: "\f110";
	position: relative;
	top: -1px;
}

#post-body .misc-pub-uploadedto:before {
	content: "\f318";
	position: relative;
	top: -1px;
}

#post-body .misc-pub-revisions:before {
	content: "\f321";
}

#post-body .misc-pub-response-to:before {
	content: "\f101";
}

#timestampdiv {
	padding-top: 5px;
	line-height: 1.76923076;
}

#timestampdiv p {
	margin: 8px 0 6px;
}

#timestampdiv input {
	text-align: center;
}

.notification-dialog {
	position: fixed;
	top: 30%;
	max-height: 70%;
	left: 50%;
	width: 450px;
	margin-left: -225px;
	background: #fff;
	box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
	line-height: 1.5;
	z-index: 1000005;
	overflow-y: auto;
}

.notification-dialog-background {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: #000;
	opacity: 0.7;
	filter: alpha(opacity=70);
	z-index: 1000000;
}

#post-lock-dialog .post-locked-message,
#post-lock-dialog .post-taken-over {
	margin: 25px;
}

#post-lock-dialog .post-locked-message a.button,
#file-editor-warning .button {
	margin-right: 10px;
}

#post-lock-dialog .post-locked-avatar {
	float: left;
	margin: 0 20px 20px 0;
}

#post-lock-dialog .wp-tab-first {
	outline: 0;
}

#post-lock-dialog .locked-saving img {
	float: left;
	margin-right: 3px;
}

#post-lock-dialog.saving .locked-saving,
#post-lock-dialog.saved .locked-saved {
	display: inline;
}

#excerpt {
	display: block;
	margin: 12px 0 0;
	height: 4em;
	width: 100%;
}

.tagchecklist {
	margin-left: 14px;
	font-size: 12px;
	overflow: auto;
}

.tagchecklist br {
	display: none;
}

.tagchecklist strong {
	margin-left: -8px;
	position: absolute;
}

.tagchecklist > li {
	float: left;
	margin-right: 25px;
	font-size: 13px;
	line-height: 1.8;
	cursor: default;
	max-width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
}

.tagchecklist .ntdelbutton {
	position: absolute;
	width: 24px;
	height: 24px;
	border: none;
	margin: 0 0 0 -19px;
	padding: 0;
	background: none;
	cursor: pointer;
	text-indent: 0;
}

#poststuff h3.hndle, /* Back-compat for pre-4.4 */
#poststuff .stuffbox > h3, /* Back-compat for pre-4.4 */
#poststuff h2 {
	font-size: 14px;
	padding: 8px 12px;
	margin: 0;
	line-height: 1.4;
}

#poststuff .stuffbox h2 {
	padding: 8px 10px;
}

#poststuff .stuffbox > h2 {
	border-bottom: 1px solid #f0f0f1;
}

#poststuff .inside {
	margin: 6px 0 0;
}

.link-php #poststuff .inside,
.link-add-php #poststuff .inside {
	margin-top: 12px;
}

#poststuff .stuffbox .inside {
	margin: 0;
}

#poststuff .inside #parent_id,
#poststuff .inside #page_template {
	max-width: 100%;
}

.post-attributes-label-wrapper {
	margin-bottom: 0.5em;
}

.post-attributes-label {
	vertical-align: baseline;
	font-weight: 600;
}

#post-visibility-select,
#comment-status-radio {
	line-height: 1.5;
	margin-top: 3px;
}

#linksubmitdiv .inside, /* Old Link Manager back-compat. */
#poststuff #submitdiv .inside {
	margin: 0;
	padding: 0;
}

#post-body-content,
.edit-form-section {
	margin-bottom: 20px;
}

.wp_attachment_details .attachment-content-description {
	margin-top: 0.5385em;
	display: inline-block;
	min-height: 1.6923em;
}

/**
* Privacy Settings section
*
* Note: This section includes selectors from
* Site Health where duplicate styling is used.
*/

/* General */
.privacy-settings #wpcontent,
.privacy-settings.auto-fold #wpcontent,
.site-health #wpcontent,
.site-health.auto-fold #wpcontent {
	padding-left: 0;
}

/* Better position for the WordPress admin notices. */
.privacy-settings .notice,
.site-health .notice {
	margin: 25px 20px 15px 22px;
}

.privacy-settings .notice ~ .notice,
.site-health .notice ~ .notice {
	margin-top: 5px;
}

/* Emulates .wrap h1 styling */
.privacy-settings-header h1,
.health-check-header h1 {
	display: inline-block;
	font-weight: 600;
	margin: 0 0.8rem 1rem;
	font-size: 23px;
	padding: 9px 0 4px;
	line-height: 1.3;
}

/* Header */
.privacy-settings-header,
.health-check-header {
	text-align: center;
	margin: 0 0 1rem;
	background: #fff;
	border-bottom: 1px solid #dcdcde;
}

.privacy-settings-title-section,
.health-check-title-section {
	display: flex;
	align-items: center;
	justify-content: center;
	clear: both;
	padding-top: 8px;
}

.privacy-settings-tabs-wrapper {
	/* IE 11 */
	display: -ms-inline-grid;
	-ms-grid-columns: 1fr 1fr;
	vertical-align: top;
	/* modern browsers */
	display: inline-grid;
	grid-template-columns: 1fr 1fr;
}

.privacy-settings-tab {
	display: block; /* IE 11 */
	text-decoration: none;
	color: inherit;
	padding: 0.5rem 1rem 1rem;
	margin: 0 1rem;
	transition: box-shadow 0.5s ease-in-out;
}

.privacy-settings-tab:nth-child(1),
.health-check-tab:nth-child(1) {
	-ms-grid-column: 1; /* IE 11 */
}

.privacy-settings-tab:nth-child(2),
.health-check-tab:nth-child(2) {
	-ms-grid-column: 2; /* IE 11 */
}

.privacy-settings-tab:focus,
.health-check-tab:focus {
	color: #1d2327;
	outline: 1px solid #787c82;
	box-shadow: none;
}

.privacy-settings-tab.active,
.health-check-tab.active {
	box-shadow: inset 0 -3px #3582c4;
	font-weight: 600;
}

/* Body */
.privacy-settings-body,
.health-check-body {
	max-width: 800px;
	margin: 0 auto;
}

.tools-privacy-policy-page th {
	min-width: 230px;
}

.hr-separator {
	margin-top: 20px;
	margin-bottom: 15px;
}

/* Accordions */
.privacy-settings-accordion,
.health-check-accordion {
	border: 1px solid #c3c4c7;
}

.privacy-settings-accordion-heading,
.health-check-accordion-heading {
	margin: 0;
	border-top: 1px solid #c3c4c7;
	font-size: inherit;
	line-height: inherit;
	font-weight: 600;
	color: inherit;
}

.privacy-settings-accordion-heading:first-child,
.health-check-accordion-heading:first-child {
	border-top: none;
}

.privacy-settings-accordion-trigger,
.health-check-accordion-trigger {
	background: #fff;
	border: 0;
	color: #2c3338;
	cursor: pointer;
	display: flex;
	font-weight: 400;
	margin: 0;
	padding: 1em 3.5em 1em 1.5em;
	min-height: 46px;
	position: relative;
	text-align: left;
	width: 100%;
	align-items: center;
	justify-content: space-between;
	-webkit-user-select: auto;
	user-select: auto;
}

.privacy-settings-accordion-trigger:hover,
.privacy-settings-accordion-trigger:active,
.health-check-accordion-trigger:hover,
.health-check-accordion-trigger:active {
	background: #f6f7f7;
}

.privacy-settings-accordion-trigger:focus,
.health-check-accordion-trigger:focus {
	color: #1d2327;
	border: none;
	box-shadow: none;
	outline-offset: -1px;
	outline: 2px solid #2271b1;
	background-color: #f6f7f7;
}

.privacy-settings-accordion-trigger .title,
.health-check-accordion-trigger .title {
	pointer-events: none;
	font-weight: 600;
	flex-grow: 1;
}

.privacy-settings-accordion-trigger .icon,
.privacy-settings-view-read .icon,
.health-check-accordion-trigger .icon,
.site-health-view-passed .icon {
	border: solid #50575e;
	border-width: 0 2px 2px 0;
	height: 0.5rem;
	pointer-events: none;
	position: absolute;
	right: 1.5em;
	top: 50%;
	transform: translateY(-70%) rotate(45deg);
	width: 0.5rem;
}

.privacy-settings-accordion-trigger .badge,
.health-check-accordion-trigger .badge {
	padding: 0.1rem 0.5rem 0.15rem;
	color: #2c3338;
	font-weight: 600;
}

.privacy-settings-accordion-trigger .badge {
	margin-left: 0.5rem;
}

.privacy-settings-accordion-trigger .badge.blue,
.health-check-accordion-trigger .badge.blue {
	border: 1px solid #72aee6;
}

.privacy-settings-accordion-trigger .badge.orange,
.health-check-accordion-trigger .badge.orange {
	border: 1px solid #dba617;
}

.privacy-settings-accordion-trigger .badge.red,
.health-check-accordion-trigger .badge.red {
	border: 1px solid #e65054;
}

.privacy-settings-accordion-trigger .badge.green,
.health-check-accordion-trigger .badge.green {
	border: 1px solid #00ba37;
}

.privacy-settings-accordion-trigger .badge.purple,
.health-check-accordion-trigger .badge.purple {
	border: 1px solid #2271b1;
}

.privacy-settings-accordion-trigger .badge.gray,
.health-check-accordion-trigger .badge.gray {
	border: 1px solid #c3c4c7;
}

.privacy-settings-accordion-trigger[aria-expanded="true"] .icon,
.privacy-settings-view-passed[aria-expanded="true"] .icon,
.health-check-accordion-trigger[aria-expanded="true"] .icon,
.site-health-view-passed[aria-expanded="true"] .icon {
	transform: translateY(-30%) rotate(-135deg)
}

.privacy-settings-accordion-panel,
.health-check-accordion-panel {
	margin: 0;
	padding: 1em 1.5em;
	background: #fff;
}

.privacy-settings-accordion-panel[hidden],
.health-check-accordion-panel[hidden] {
	display: none;
}

.privacy-settings-accordion-panel a .dashicons,
.health-check-accordion-panel a .dashicons {
	text-decoration: none;
}

.privacy-settings-accordion-actions {
	text-align: right;
	display: block;
}

.privacy-settings-accordion-actions .success {
	display: none;
	color: #007017;
	padding-right: 1em;
	padding-top: 6px;
}

.privacy-settings-accordion-actions .success.visible {
	display: inline-block;
}

/* Suggested text for privacy policy */
.privacy-settings-accordion-panel.hide-privacy-policy-tutorial .wp-policy-help, /* For back-compat, see #49282 */
.privacy-settings-accordion-panel.hide-privacy-policy-tutorial .privacy-policy-tutorial,
.privacy-settings-accordion-panel.hide-privacy-policy-tutorial .privacy-text-copy {
	display: none;
}

.privacy-settings-accordion-panel strong.wp-policy-help, /* For back-compat, see #49282 */
.privacy-settings-accordion-panel strong.privacy-policy-tutorial {
	display: block;
	margin: 0 0 1em;
}

.privacy-text-copy span {
	pointer-events: none;
}

.privacy-settings-accordion-panel .wp-suggested-text > *:not(h1):not(h2):not(h3):not(h4):not(h5):not(h6):not(div):not(.privacy-policy-tutorial):not(.wp-policy-help):not(.privacy-text-copy):not(span.success):not(.notice p),
.privacy-settings-accordion-panel .wp-suggested-text div > *:not(h1):not(h2):not(h3):not(h4):not(h5):not(h6):not(div):not(.privacy-policy-tutorial):not(.wp-policy-help):not(.privacy-text-copy):not(span.success):not(.notice p),
.privacy-settings-accordion-panel > *:not(h1):not(h2):not(h3):not(h4):not(h5):not(h6):not(div):not(.privacy-policy-tutorial):not(.wp-policy-help):not(.privacy-text-copy):not(span.success):not(.notice p),
.privacy-settings-accordion-panel div > *:not(h1):not(h2):not(h3):not(h4):not(h5):not(h6):not(div):not(.privacy-policy-tutorial):not(.wp-policy-help):not(.privacy-text-copy):not(span.success):not(.notice p) {
	margin: 0;
	padding: 1em;
	border-left: 2px solid #787c82;
}

/* Media queries */
@media screen and (max-width: 782px) {

	.privacy-settings-body,
	.health-check-body {
		margin: 0 12px;
		width: auto;
	}

	.privacy-settings .notice,
	.site-health .notice {
		margin: 5px 10px 15px;
	}

	.privacy-settings .update-nag,
	.site-health .update-nag {
		margin-right: 10px;
		margin-left: 10px;
	}

	input#create-page {
		margin-top: 10px;
	}

	.wp-core-ui button.privacy-text-copy {
		white-space: normal;
		line-height: 1.8;
	}

	#edit-slug-box {
		padding: 0;
	}

	#titlewrap .skiplink:focus {
		top: 5px;
	}
}

@media only screen and (max-width: 1004px) {

	.privacy-settings-body,
	.health-check-body {
		margin: 0 22px;
		width: auto;
	}
}

/**
* End Privacy Settings section
*/

/*------------------------------------------------------------------------------
  11.1 - Custom Fields
------------------------------------------------------------------------------*/

#postcustomstuff thead th {
	padding: 5px 8px 8px;
	background-color: #f0f0f1;
}

#postcustom #postcustomstuff .submit {
	border: 0 none;
	float: none;
	padding: 0 8px 8px;
}

#postcustom #postcustomstuff .add-custom-field {
	padding: 12px 8px 8px;
}

#side-sortables #postcustom #postcustomstuff .submit {
	margin: 0;
	padding: 0;
}

#side-sortables #postcustom #postcustomstuff #the-list textarea {
	height: 85px;
}

#side-sortables #postcustom #postcustomstuff td.left input,
#side-sortables #postcustom #postcustomstuff td.left select,
#side-sortables #postcustomstuff #newmetaleft a {
	margin: 3px 3px 0;
}

#postcustomstuff table {
	margin: 0;
	width: 100%;
	border: 1px solid #dcdcde;
	border-spacing: 0;
	background-color: #f6f7f7;
}

#postcustomstuff tr {
	vertical-align: top;
}

#postcustomstuff table input,
#postcustomstuff table select,
#postcustomstuff table textarea {
	width: 96%;
	margin: 8px;
}

#side-sortables #postcustomstuff table input,
#side-sortables #postcustomstuff table select,
#side-sortables #postcustomstuff table textarea {
	margin: 3px;
}

#postcustomstuff th.left,
#postcustomstuff td.left {
	width: 38%;
}

#postcustomstuff .submit input {
	margin: 0;
	width: auto;
}

#postcustomstuff #newmetaleft a,
#postcustomstuff #newmeta-button {
	display: inline-block;
	margin: 0 8px 8px;
	text-decoration: none;
}

.no-js #postcustomstuff #enternew {
	display: none;
}

#post-body-content .compat-attachment-fields {
	margin-bottom: 20px;
}

.compat-attachment-fields th {
	padding-top: 5px;
	padding-right: 10px;
}

/*------------------------------------------------------------------------------
  11.3 - Featured Images
------------------------------------------------------------------------------*/

#select-featured-image {
	padding: 4px 0;
	overflow: hidden;
}

#select-featured-image img {
	max-width: 100%;
	height: auto;
	margin-bottom: 10px;
}

#select-featured-image a {
	float: left;
	clear: both;
}

#select-featured-image .remove {
	display: none;
	margin-top: 10px;
}

.js #select-featured-image.has-featured-image .remove {
	display: inline-block;
}

.no-js #select-featured-image .choose {
	display: none;
}

/*------------------------------------------------------------------------------
  11.4 - Post formats
------------------------------------------------------------------------------*/

.post-format-icon::before {
	display: inline-block;
	vertical-align: middle;
	height: 20px;
	width: 20px;
	margin-top: -4px;
	margin-right: 7px;
	color: #dcdcde;
	font: normal 20px/1 dashicons;
	speak: never;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

a.post-format-icon:hover:before {
	color: #135e96;
}

#post-formats-select {
	line-height: 2;
}

#post-formats-select .post-format-icon::before {
	top: 5px;
}

input.post-format {
	margin-top: 1px;
}

label.post-format-icon {
	margin-left: 0;
	padding: 2px 0;
}

.post-format-icon.post-format-standard::before {
	content: "\f109";
}

.post-format-icon.post-format-image::before {
	content: "\f128";
}

.post-format-icon.post-format-gallery::before {
	content: "\f161";
}

.post-format-icon.post-format-audio::before {
	content: "\f127";
}

.post-format-icon.post-format-video::before {
	content: "\f126";
}

.post-format-icon.post-format-chat::before {
	content: "\f125";
}

.post-format-icon.post-format-status::before {
	content: "\f130";
}

.post-format-icon.post-format-aside::before {
	content: "\f123";
}

.post-format-icon.post-format-quote::before {
	content: "\f122";
}

.post-format-icon.post-format-link::before {
	content: "\f103";
}

/*------------------------------------------------------------------------------
  12.0 - Categories
------------------------------------------------------------------------------*/

.category-adder {
	margin-left: 120px;
	padding: 4px 0;
}

.category-adder h4 {
	margin: 0 0 8px;
}

#side-sortables .category-adder {
	margin: 0;
}

.wp-tab-panel,
.categorydiv div.tabs-panel,
.customlinkdiv div.tabs-panel,
.posttypediv div.tabs-panel,
.taxonomydiv div.tabs-panel {
	min-height: 42px;
	max-height: 200px;
	overflow: auto;
	padding: 0 0.9em;
	border: solid 1px #dcdcde;
	background-color: #fff;
}

div.tabs-panel-active {
	display: block;
}

div.tabs-panel-inactive {
	display: none;
}

div.tabs-panel-active:focus {
	box-shadow: inset 0 0 0 2px #2271b1;
	/* Only visible in Windows High Contrast mode */
	outline: 2px solid transparent;
}
.options-discussion-php .indent-children ul,
#front-page-warning,
#front-static-pages ul,
ul.export-filters,
.inline-editor ul.cat-checklist ul,
.categorydiv ul.categorychecklist ul,
.customlinkdiv ul.categorychecklist ul,
.posttypediv ul.categorychecklist ul,
.taxonomydiv ul.categorychecklist ul {
	margin-left: 18px;
}

ul.categorychecklist li {
	margin: 0;
	padding: 0;
	line-height: 1.69230769;
	word-wrap: break-word;
}

.categorydiv .tabs-panel,
.customlinkdiv .tabs-panel,
.posttypediv .tabs-panel,
.taxonomydiv .tabs-panel {
	border-width: 3px;
	border-style: solid;
}

.form-wrap label {
	display: block;
	padding: 2px 0;
}

.form-field input[type="text"],
.form-field input[type="password"],
.form-field input[type="email"],
.form-field input[type="number"],
.form-field input[type="search"],
.form-field input[type="tel"],
.form-field input[type="url"],
.form-field textarea {
	border-style: solid;
	border-width: 1px;
	width: 95%;
}

.form-field select,
.form-field p {
	max-width: 95%;
}

p.description,
.form-wrap p {
	margin: 2px 0 5px;
	color: #646970;
}

p.help,
p.description,
span.description,
.form-wrap p {
	font-size: 13px;
}

p.description code {
	font-style: normal;
}

.form-wrap .form-field {
	margin: 1em 0;
	padding: 0;
}

.col-wrap h2 {
	margin: 12px 0;
	font-size: 1.1em;
}

.col-wrap p.submit {
	margin-top: -10px;
}

.edit-term-notes {
	margin-top: 2em;
}

/*------------------------------------------------------------------------------
  13.0 - Tags
------------------------------------------------------------------------------*/

#poststuff .tagsdiv .ajaxtag {
	margin-top: 1em;
}

#poststuff .tagsdiv .howto {
	margin: 1em 0 6px;
}

.ajaxtag .newtag {
	position: relative;
}

.tagsdiv .newtag {
	width: 180px;
}

.tagsdiv .the-tags {
	display: block;
	height: 60px;
	margin: 0 auto;
	overflow: auto;
	width: 260px;
}

#post-body-content .tagsdiv .the-tags {
	margin: 0 5px;
}

p.popular-tags {
	border: none;
	line-height: 2em;
	padding: 8px 12px 12px;
	text-align: justify;
}

p.popular-tags a {
	padding: 0 3px;
}

.tagcloud {
	width: 97%;
	margin: 0 0 40px;
	text-align: justify;
}

.tagcloud h2 {
	margin: 2px 0 12px;
}

#poststuff .inside .the-tagcloud {
	margin: 5px 0 10px;
	padding: 8px;
	border: 1px solid #dcdcde;
	line-height: 1.2;
	word-spacing: 3px;
}

.the-tagcloud ul {
	margin: 0;
}

.the-tagcloud ul li {
	display: inline-block;
}

/* Back-compat styles from deprecated jQuery.suggest, see ticket #40260. */
.ac_results {
	display: none;
	margin: -1px 0 0;
	padding: 0;
	list-style: none;
	position: absolute;
	z-index: 10000;
	border: 1px solid #4f94d4;
	background-color: #fff;
}

.wp-customizer .ac_results {
	z-index: 500000;
}

.ac_results li {
	margin: 0;
	padding: 5px 10px;
	white-space: nowrap;
	text-align: left;
}

.ac_results .ac_over,
.ac_over .ac_match {
	background-color: #2271b1;
	color: #fff;
	cursor: pointer;
}

.ac_match {
	text-decoration: underline;
}

#addtag .spinner {
	float: none;
	vertical-align: top;
}

#edittag {
	max-width: 800px;
}

.edit-tag-actions {
	margin-top: 20px;
}

/* Comments */

.comment-php .wp-editor-area {
	height: 200px;
}

.comment-ays th,
.comment-ays td {
	padding: 10px 15px;
}

.comment-ays .comment-content ul {
	list-style: initial;
	margin-left: 2em;
}

.comment-ays .comment-content a[href]:after {
	content: "(" attr( href ) ")";
	display: inline-block;
	padding: 0 4px;
	color: #646970;
	font-size: 13px;
	word-break: break-all;
}

.comment-ays .comment-content p.edit-comment {
	margin-top: 10px;
}

.comment-ays .comment-content p.edit-comment a[href]:after {
	content: "";
	padding: 0;
}

.comment-ays-submit .button-cancel {
	margin-left: 1em;
}

.trash-undo-inside,
.spam-undo-inside {
	margin: 1px 8px 1px 0;
	line-height: 1.23076923;
}

.spam-undo-inside .avatar,
.trash-undo-inside .avatar {
	height: 20px;
	width: 20px;
	margin-right: 8px;
	vertical-align: middle;
}

.stuffbox .editcomment {
	clear: none;
	margin-top: 0;
}

#namediv.stuffbox .editcomment input {
	width: 100%;
}

#namediv.stuffbox .editcomment.form-table td {
	padding: 10px;
}

#comment-status-radio p {
	margin: 3px 0 5px;
}

#comment-status-radio input {
	margin: 2px 3px 5px 0;
	vertical-align: middle;
}

#comment-status-radio label {
	padding: 5px 0;
}

/* links tables */
table.links-table {
	width: 100%;
	border-spacing: 0;
}

.links-table th {
	font-weight: 400;
	text-align: left;
	vertical-align: top;
	min-width: 80px;
	width: 20%;
	word-wrap: break-word;
}

.links-table th,
.links-table td {
	padding: 5px 0;
}

.links-table td label {
	margin-right: 8px;
}

.links-table td input[type="text"],
.links-table td textarea {
	width: 100%;
}

.links-table #link_rel {
	max-width: 280px;
}

/* DFW 2
-------------------------------------------------------------- */

#qt_content_dfw {
	display: none;
}

.wp-editor-expand #qt_content_dfw {
	display: inline-block;
}

.focus-on .wrap > h1,
.focus-on .page-title-action,
.focus-on #wpfooter,
.focus-on .postbox-container > *,
.focus-on div.updated,
.focus-on div.error,
.focus-on div.notice,
.focus-on .update-nag,
.focus-on #wp-toolbar,
.focus-on #screen-meta-links,
.focus-on #screen-meta {
	opacity: 0;
	transition-duration: 0.6s;
	transition-property: opacity;
	transition-timing-function: ease-in-out;
}

.focus-on #wp-toolbar {
	opacity: 0.3;
}

.focus-off .wrap > h1,
.focus-off .page-title-action,
.focus-off #wpfooter,
.focus-off .postbox-container > *,
.focus-off div.updated,
.focus-off div.error,
.focus-off div.notice,
.focus-off .update-nag,
.focus-off #wp-toolbar,
.focus-off #screen-meta-links,
.focus-off #screen-meta {
	opacity: 1;
	transition-duration: 0.2s;
	transition-property: opacity;
	transition-timing-function: ease-in-out;
}

.focus-off #wp-toolbar {
	-webkit-transform: translate(0, 0);
}

.focus-on #adminmenuback,
.focus-on #adminmenuwrap {
	transition-duration: 0.6s;
	transition-property: transform;
	transition-timing-function: ease-in-out;
}

.focus-on #adminmenuback,
.focus-on #adminmenuwrap {
	transform: translateX( -100% );
}

.focus-off #adminmenuback,
.focus-off #adminmenuwrap {
	transform: translateX( 0 );
	transition-duration: 0.2s;
	transition-property: transform;
	transition-timing-function: ease-in-out;
}

/* =Media Queries
-------------------------------------------------------------- */

/**
 * HiDPI Displays
 */
@media print,
  (min-resolution: 120dpi) {
	#content-resize-handle,
	#post-body .wp_themeSkin .mceStatusbar a.mceResize {
		background: transparent url(../images/resize-2x.gif) no-repeat scroll right bottom;
		background-size: 11px 11px;
	}

	/*rtl:ignore*/
	.rtl #content-resize-handle,
	.rtl #post-body .wp_themeSkin .mceStatusbar a.mceResize {
		background-image: url(../images/resize-rtl-2x.gif);
		background-position: left bottom;
	}
}

/*
 * The edit attachment screen auto-switches to one column layout when the
 * viewport is smaller than 1200 pixels.
 */
@media only screen and (max-width: 1200px) {
	.post-type-attachment #poststuff {
		min-width: 0;
	}

	.post-type-attachment #wpbody-content #poststuff #post-body {
		margin: 0;
	}

	.post-type-attachment #wpbody-content #post-body.columns-2 #postbox-container-1 {
		margin-right: 0;
		width: 100%;
	}

	.post-type-attachment #poststuff #postbox-container-1 .empty-container,
	.post-type-attachment #poststuff #postbox-container-1 #side-sortables:empty {
		outline: none;
		height: 0;
		min-height: 0;
	}

	.post-type-attachment #poststuff #post-body.columns-2 #side-sortables {
		min-height: 0;
		width: auto;
	}

	.is-dragging-metaboxes.post-type-attachment #post-body .meta-box-sortables {
		outline: none;
		min-height: 0;
		margin-bottom: 0;
	}

	/* hide the radio buttons for column prefs */
	.post-type-attachment .screen-layout,
	.post-type-attachment .columns-prefs {
		display: none;
	}
}

/* one column on the post write/edit screen */
@media only screen and (max-width: 850px) {
	#poststuff {
		min-width: 0;
	}

	#wpbody-content #poststuff #post-body {
		margin: 0;
	}

	#wpbody-content #post-body.columns-2 #postbox-container-1 {
		margin-right: 0;
		width: 100%;
	}

	#poststuff #postbox-container-1 .empty-container,
	#poststuff #postbox-container-1 #side-sortables:empty {
		height: 0;
		min-height: 0;
	}

	#poststuff #post-body.columns-2 #side-sortables {
		min-height: 0;
		width: auto;
	}

	/* Increase min-height while dragging for the #side-sortables and any potential sortables area with custom ID. */
	.is-dragging-metaboxes #poststuff #postbox-container-1 .empty-container,
	.is-dragging-metaboxes #poststuff #postbox-container-1 #side-sortables:empty,
	.is-dragging-metaboxes #poststuff #post-body.columns-2 #side-sortables,
	.is-dragging-metaboxes #poststuff #post-body.columns-2 .meta-box-sortables {
		height: auto;
		min-height: 60px;
	}

	/* hide the radio buttons for column prefs */
	.screen-layout,
	.columns-prefs {
		display: none;
	}
}

@media screen and (max-width: 782px) {
	.wp-core-ui .edit-tag-actions .button-primary {
		margin-bottom: 0;
	}

	#post-body-content {
		min-width: 0;
	}

	#titlediv #title-prompt-text {
		padding: 10px;
	}

	#poststuff .stuffbox .inside {
		padding: 0 2px 4px 0;
	}

	#poststuff h3.hndle, /* Back-compat for pre-4.4 */
	#poststuff .stuffbox > h3, /* Back-compat for pre-4.4 */
	#poststuff h2 {
		padding: 12px;
	}

	#namediv.stuffbox .editcomment.form-table td {
		padding: 5px 10px;
	}

	.post-format-options {
		padding-right: 0;
	}

	.post-format-options a {
		margin-right: 5px;
		margin-bottom: 5px;
		min-width: 52px;
	}

	.post-format-options .post-format-title {
		font-size: 11px;
	}

	.post-format-options a div {
		height: 28px;
		width: 28px;
	}

	.post-format-options a div:before {
		font-size: 26px !important;
	}

	/* Publish Metabox Options */
	#post-visibility-select {
		line-height: 280%;
	}

	.wp-core-ui .save-post-visibility,
	.wp-core-ui .save-timestamp {
		vertical-align: middle;
		margin-right: 15px;
	}

	.timestamp-wrap select#mm {
		display: block;
		width: 100%;
		margin-bottom: 10px;
	}

	.timestamp-wrap #jj,
	.timestamp-wrap #aa,
	.timestamp-wrap #hh,
	.timestamp-wrap #mn {
		padding: 12px 3px;
		font-size: 14px;
		margin-bottom: 5px;
		width: auto;
		text-align: center;
	}

	/* Categories Metabox */
	ul.category-tabs {
		margin: 30px 0 15px;
	}

	ul.category-tabs li.tabs {
		padding: 15px;
	}

	ul.categorychecklist li {
		margin-bottom: 15px;
	}

	ul.categorychecklist ul {
		margin-top: 15px;
	}

	.category-add input[type=text],
	.category-add select {
		max-width: none;
		margin-bottom: 15px;
	}

	/* Tags Metabox */
	.tagsdiv .newtag {
		width: 100%;
		height: auto;
		margin-bottom: 15px;
	}

	.tagchecklist {
		margin: 25px 10px;
	}

	.tagchecklist > li {
		font-size: 16px;
		line-height: 1.4;
	}

	/* Discussion */
	#commentstatusdiv p {
		line-height: 2.8;
	}

	/* TinyMCE Adjustments */
	.mceToolbar * {
		white-space: normal !important;
	}

	.mceToolbar tr,
	.mceToolbar td {
		float: left !important;
	}

	.wp_themeSkin a.mceButton {
		width: 30px;
		height: 30px;
	}

	.wp_themeSkin .mceButton .mceIcon {
		margin-top: 5px;
		margin-left: 5px;
	}

	.wp_themeSkin .mceSplitButton {
		margin-top: 1px;
	}

	.wp_themeSkin .mceSplitButton td a.mceAction {
		padding: 6px 3px 6px 6px;
	}

	.wp_themeSkin .mceSplitButton td a.mceOpen,
	.wp_themeSkin .mceSplitButtonEnabled:hover td a.mceOpen {
		padding-top: 6px;
		padding-bottom: 6px;
		background-position: 1px 6px;
	}

	.wp_themeSkin table.mceListBox {
		margin: 5px;
	}

	div.quicktags-toolbar input {
		padding: 10px 20px;
	}

	button.wp-switch-editor {
		font-size: 16px;
		line-height: 1;
		margin: 7px 0 0 7px;
		padding: 8px 12px;
	}

	#wp-content-media-buttons a {
		font-size: 14px;
		padding: 6px 10px;
	}

	.wp-media-buttons span.wp-media-buttons-icon,
	.wp-media-buttons span.jetpack-contact-form-icon {
		width: 22px !important;
		margin-left: -2px !important;
	}

	.wp-media-buttons .add_media span.wp-media-buttons-icon:before,
	.wp-media-buttons #insert-jetpack-contact-form span.jetpack-contact-form-icon:before {
		font-size: 20px !important;
	}

	#content_wp_fullscreen {
		display: none;
	}

	.misc-pub-section {
		padding: 20px 10px;
	}

	#delete-action,
	#publishing-action {
		line-height: 3.61538461;
	}

	#publishing-action .spinner {
		float: none;
		margin-top: -2px; /* Half of the Publish button's bottom margin. */
	}

	/* Moderate Comment */
	.comment-ays th,
	.comment-ays td {
		padding-bottom: 0;
	}

	.comment-ays td {
		padding-top: 6px;
	}

	/* Links */
	.links-table #link_rel {
		max-width: none;
	}

	.links-table th,
	.links-table td {
		padding: 10px 0;
	}

	.edit-term-notes {
		display: none;
	}

	.privacy-text-box {
		width: auto;
	}

	.privacy-text-box-toc {
		float: none;
		width: auto;
		height: 100%;
		display: flex;
		flex-direction: column;
	}

	.privacy-text-section .return-to-top {
		margin: 2em 0 0;
	}
}
