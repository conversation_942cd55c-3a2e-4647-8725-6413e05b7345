/**
 * All of the CSS for your admin-specific functionality should be
 * included in this file.
 */
#cli-plugin-migrate {
	background: #C5E3BF;
	border: 1px solid #98A148;
	color: #000;
	margin: 0 0 20px 0;
	padding: 10px;
	display: none;
}

.vvv_combobox {
	width: 100%;
}

.vvv_textbox {
	height: 150px;
	width: 100%;
}

.form-table input[type="text"], .vvv_textfield {
	width: 100%;
	margin-bottom: 5px;
}

.cli-plugin-example {
	display: block;
}

#cookielawinfo-accordion h4 {
	border-bottom: 1px solid #ccc;
	line-height: 110%;
	padding: 5px;
}

#cookielawinfo-accordion h4 code {
	padding-left: 40px;
	background: transparent;
}

.cli-help pre {
	font-weight: bold;
}

.cli-help span {
	margin: 0 0 30px 15px;
	display: block;
}

.cli-plugin-toolbar {
	height: 40px;
	width: 100%;
	margin: 0;
	padding: 0;
}

.cli-plugin-toolbar .left {
	float: left;
	margin: 0;
	padding: 0;
}

.cli-plugin-toolbar .left img {
	vertical-align: text-bottom;
	margin-right: 10px;
}

.cli-plugin-toolbar .right {
	float: right;
	margin: 0 10px 0 0;
	padding: 0;
}

.cli-plugin-toolbar.top {
	margin-bottom: -5px;
}

.cli-plugin-toolbar.bottom {
	margin-top: 12px;
	background: #f5f5f5;
	border-top: 1px solid #ddd;
	margin-left: -15px;
	margin-right: -15px;
	margin-bottom: -15px;
	padding: 15px;
}

.cli-plugin-toolbar.top {
	margin-top: -15px;
	background: #f5f5f5;
	border-bottom: 1px solid #ddd;
	border-top: 1px solid #ddd;
	margin-left: -15px;
	margin-right: -15px;
	margin-bottom: -15px;
	padding: 15px;
}

#header_on_off_field_warning {
	margin-left: 30px;
}

.warning {
	/* called by jQuery in admin-ui-controller.js */
	color: #f00;
	font-weight: bold;
}

.cli-plugin-container {
	overflow: hidden;
	width: 100%;
}

.cli-plugin-left-col {
	float: left;
	padding-bottom: 500em;
	margin-bottom: -500em;
}

.cli-plugin-right-col {
	float: left;
	margin-right: -1px;
	/* For IE */
	padding-bottom: 500em;
	margin-bottom: -500em;
}

.cli-plugin-container.width-50 {
	width: 50%;
}

.cli-plugin-container.width-60 {
	width: 60%;
}

.cli-plugin-container.width-70 {
	width: 70%;
}

.cli-plugin-container.width-80 {
	width: 80%;
}

.cli-plugin-container.width-90 {
	width: 90%;
}

.cli-plugin-left-col.width-50, .cli-plugin-right-col.width-50 {
	width: 50%;
}

.cli-plugin-left-col.width-62, .cli-plugin-right-col.width-62 {
	width: 62%;
}

/* Golden Ratio */
.cli-plugin-left-col.width-38, .cli-plugin-right-col.width-38 {
	width: 38%;
}

/* Golden Ratio */
.cli-plugin-left-col.width-f220, .cli-plugin-right-col.width-f220 {
	width: 220px;
}

.cli-plugin-container div.pad-5, .cli-plugin-left-col div.pad-5, .cli-plugin-right-col div.pad-5 {
	padding: 5px;
}

.cli-plugin-container div.pad-10, .cli-plugin-left-col div.pad-10, .cli-plugin-right-col div.pad-10 {
	padding: 10px;
}

.width-60 {
	width: 60%;
}

.width-100 {
	width: 100%;
}

.hr-top {
	border-top: 1px solid #ccc;
}

.hr-bottom {
	border-bottom: 1px solid #ccc;
}





table.cli_script_items {
	position: relative
}

table.cli_script_items td, table.cli_script_items th {
	display: table-cell !important;
	padding: 1em !important;
	vertical-align: top;
	line-height: 1.75em
}

table.wc_emails.wc_emails td, table.cli_script_items.wc_emails td, table.wc_shipping.wc_emails td {
	vertical-align: middle
}

table.cli_script_items tr:nth-child(odd) td {
	background: #f9f9f9
}

table.cli_script_items td.name {
	font-weight: 700
}

table.wc_emails .settings, table.cli_script_items .settings, table.wc_shipping .settings {
	text-align: right
}

table.wc_emails .default, table.wc_emails .radio, table.wc_emails .status, table.cli_script_items .default, table.cli_script_items .radio, table.cli_script_items .status, table.wc_shipping .default, table.wc_shipping .radio, table.wc_shipping .status {
	text-align: center
}

table.wc_emails .default .tips, table.wc_emails .radio .tips, table.wc_emails .status .tips, table.cli_script_items .default .tips, table.cli_script_items .radio .tips, table.cli_script_items .status .tips, table.wc_shipping .default .tips, table.wc_shipping .radio .tips, table.wc_shipping .status .tips {
	margin: 0 auto
}

table.wc_emails .default input, table.wc_emails .radio input, table.wc_emails .status input, table.cli_script_items .default input, table.cli_script_items .radio input, table.cli_script_items .status input, table.wc_shipping .default input, table.wc_shipping .radio input, table.wc_shipping .status input {
	margin: 0
}

table.wc_emails td.sort, table.cli_script_items td.sort, table.wc_shipping td.sort {
	cursor: move;
	font-size: 15px;
	text-align: center
}

table.wc_emails td.sort::before, table.cli_script_items td.sort::before, table.wc_shipping td.sort::before {
	content: '\f333';
	font-family: Dashicons;
	text-align: center;
	line-height: 1;
	color: #999;
	display: block;
	width: 17px;
	float: left;
	height: 100%;
	line-height: 24px
}

table.wc_emails .wc-payment-gateway-method-name, table.cli_script_items .wc-payment-gateway-method-name, table.wc_shipping .wc-payment-gateway-method-name {
	font-weight: 400
}

table.wc_emails .wc-email-settings-table-name, table.cli_script_items .wc-email-settings-table-name, table.wc_shipping .wc-email-settings-table-name {
	font-weight: 700
}

table.wc_emails .wc-email-settings-table-name span, table.cli_script_items .wc-email-settings-table-name span, table.wc_shipping .wc-email-settings-table-name span {
	font-weight: 400;
	color: #999;
	margin: 0 0 0 4px !important
}

table.wc_emails .wc-payment-gateway-method-toggle-disabled, table.wc_emails .cli-script-items-toggle-enabled, table.cli_script_items .wc-payment-gateway-method-toggle-disabled, table.cli_script_items .cli-script-items-toggle-enabled, table.wc_shipping .wc-payment-gateway-method-toggle-disabled, table.wc_shipping .cli-script-items-toggle-enabled {
	padding-top: 1px;
	display: block;
	outline: 0;
	-webkit-box-shadow: none;
	box-shadow: none
}

table.wc_emails .wc-email-settings-table-status, table.cli_script_items .wc-email-settings-table-status, table.wc_shipping .wc-email-settings-table-status {
	text-align: center;
	width: 1em
}

table.wc_emails .wc-email-settings-table-status .tips, table.cli_script_items .wc-email-settings-table-status .tips, table.wc_shipping .wc-email-settings-table-status .tips {
	margin: 0 auto
}

.cli-input-toggle {
	height: 16px;
	width: 32px;
	border: 2px solid #935687;
	background-color: #935687;
	display: inline-block;
	text-indent: -9999px;
	border-radius: 10em;
	position: relative;
	margin-top: -1px;
	vertical-align: text-top
}

.cli-input-toggle:before {
	content: "";
	display: block;
	width: 16px;
	height: 16px;
	background: #fff;
	position: absolute;
	top: 0;
	right: 0;
	border-radius: 100%
}

.cli-input-toggle.cli-input-toggle--disabled {
	border-color: #999;
	background-color: #999
}

.cli-input-toggle.cli-input-toggle--disabled:before {
	right: auto;
	left: 0
}

.cli-input-toggle.cli-input-toggle--loading {
	opacity: .5
}

.cookie-law-info-tab-head {
	margin-right: 0px;
	border-bottom: none;
}

.cookie-law-info-tab-container {
	padding: 15px;
	background: #fff;
	box-shadow: 0px 2px 2px #ccc;
	float: left;
	box-sizing: border-box;
	width: 100%;
	height: auto;
}

.cookie-law-info-tab-head .nav-tab-active {
	background: #fff;
	border-bottom: solid 1px #fff;
}

.cookie-law-info-tab-head .nav-tab:focus {
	box-shadow: none;
}

.cookie-law-info-tab-content {
	display: none;
	float: left;
	width: 100%;
	height: auto;
}

.cli_sub_tab_container {
	float: left;
	width: 100%;
	height: auto;
}

.cli_sub_tab {
	display: inline-block;
	margin: 0px;
	float: left;
	width: 100%;
	height: auto;
}

.cli_sub_tab li {
	display: inline-block;
	border-left: solid 1px #ccc;
	padding: 3px 10px;
	cursor: pointer;
}

.cli_sub_tab_content {
	display: none;
	float: left;
	width: 100%;
	height: auto;
}

.cli-shortcodes li {
	margin-bottom: 20px;
	border-bottom: dashed 1px #ccc;
	padding-bottom: 7px;
	margin-left: 15px;
}

.cli-shortcodes li div {
	font-weight: bold;
	width: 100%;
}

.cli-shortcodes li span {
	display: inline-block;
}

.cli-help-links li {
	float: left;
	padding: 40px;
	margin: 20px;
	display: inline-block;
	text-align: center;
	box-shadow: 1px 1px 5px 1px rgba(0, 0, 0, .1);
	width: 185px;
	height: 245px;
}

.cli-help-links li a {
	text-decoration: none;
	height: 28px !important;
	margin-top: 20px;
}

.cli-help-links li img {
	margin-top: 15px;
}

/* copied from bootstrap */
.cli_sub_tab_container input[type="text"], .cli_sub_tab_container select {
	display: block;
	width: 100%;
	padding: 0.375rem 0.75rem;
	font-size: 1rem;
	line-height: 1.5;
	color: #495057;
	background-color: #fff;
	background-clip: padding-box;
	border: 1px solid #ced4da;
	border-radius: 0.25rem;
	transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
	height: auto !important;
}

.cookie-law-info-tab-container .button-primary {
	height: 34px;
}

.notify_msg {
	position: fixed;
	width: 300px;
	padding: 15px;
	color: #fff;
	right: 60px;
	top: 0px;
	opacity: 0;
	box-shadow: 0px 2px 2px #ccc;
	border-radius: 5px;
}

.cli-indent-15 th {
	padding-left: 15px;
}

.cli_notify_table {
	height: 60px;
}

.cookie-law-info-form-container {
	padding: 15px;
	background: #fff;
	box-shadow: 0px 2px 2px #ccc;
}

.cli-admin-table label {
	width: 100%;
	display: inline-block;
	font-weight: bold;
	margin-bottom: 10px;
	margin-top: 15px;
}

.cli_non_necessary_form .cli_form_help {
	color: #aaa;
	font-style: italic;
	font-weight: 250;
	font-size: 12px;
}

.cli_settings_left {
	width: 68%;
	float: left;
	margin-bottom: 25px;
}

.cli_settings_right {
	width: calc(31% - 20px);
	float: left;
	margin-left: 25px;
}

.cli_privacy_overview_form label {
	width: 100%;
	display: inline-block;
	font-weight: bold;
	margin-bottom: 10px;
	margin-top: 15px;
}

.cli_privacy_overview_form .cli_form_help {
	color: #aaa;
	font-style: italic;
	font-weight: 250;
	font-size: 12px;
}

.cli_form_help {
	color: #8e8989;
	font-style: italic;
	font-weight: 250;
	font-size: 12px;
	display: inline-block;
	width: 100%;
}

.cli_form_er {
	color: red;
	font-style: italic;
	font-weight: 300;
	font-size: 12px;
	display: inline-block;
	width: 100%;
}

.cli_scroll_accept_er {
	display: none;
}

.cli_premium_features {
	font-size: 14px;
}

@media screen and (max-width:1210px) {
	.cli_settings_left {
		width: 100%;
	}

	.cli_settings_right {
		padding-left: 0px;
		width: 100%;
	}
}

/* CCPA Related Changes */
/* Accordion Styles  */

.wt-cli-accordion-container {
	position: relative;
	height: auto;
	border-left: 1px solid #e2e4e7;
	border-right: 1px solid #e2e4e7;
}

.wt-cli-accordion-container>h2 {
	text-align: center;
	color: #fff;
	padding-bottom: 5px;
	margin-bottom: 20px;
	padding-bottom: 15px;
}

.wt-cli-accordion-content>.wt-cli-accordion-tab {
	padding-left: 10px;
	padding-right: 10px;
	border-left: 1px solid #e2e4e7;
	border-right: 1px solid #e2e4e7;
}

.wt-cli-accordion-content.active>.wt-cli-accordion-tab {
	background: #fff;
}

.wt-cli-accordion-tab {
	position: relative;
	width: 100%;
	height: auto;
	border-bottom: 2px dotted #e2e4e7;
	box-sizing: border-box;
}

.wt-cli-accordion-content {
	padding-bottom: 15px;
}

.wt-cli-accordion-tab:not(:first-child) {
	margin-top: -1px;
}

.wt-cli-accordion-tab>a {
	display: block;
	text-decoration: none;
	color: #191e23;
	font-weight: 600;
	padding-top: 15px;
	padding-bottom: 15px;
	outline: none;
	-webkit-transition: all 0.2s linear;
	-moz-transition: all 0.2s linear;
	transition: all 0.2s linear;
	box-shadow: none;
	display: flex;
	align-items: center;
	font-size: 1.3em;
}

.wt-cli-accordion-tab>a i {
	float: right;
	margin-top: 2px;
}

.wt-cli-accordion-tab>a:before {
	content: "";
	display: block;
	width: 8px;
	height: 8px;
	border-top: 2px solid #000000;
	border-right: 2px solid #000000;
	-webkit-transform: rotate(45deg);
	transform: rotate(45deg);
	transition: -webkit-transform 400ms ease;
	transition: transform 400ms ease;
	transition: transform 400ms ease, -webkit-transform 400ms ease;
	opacity: 0.8;
	margin-right: 16px;
}

.wt-cli-accordion-tab>a.active::before {
	-webkit-transform: rotate(135deg);
	transform: rotate(135deg);
}

.wt-cli-accordion-content {
	display: none;
}

/* End  Accordion Styles  */

.wt-cli-section-gdpr-ccpa .wt-cli-section-inner {
	display: none;
}

/* Webtoffe GDPR Cookie Consent Tooltip */

/* Add this attribute to the element that needs a tooltip */

[data-wt-cli-tooltip] {
	position: relative;
	z-index: 9999;
	cursor: pointer;
}

/* Hide the tooltip content by default */

[data-wt-cli-tooltip]:before, [data-wt-cli-tooltip]:after {
	visibility: hidden;
	opacity: 0;
	pointer-events: none;
}

/* Position tooltip above the element */

[data-wt-cli-tooltip]:before {
	position: absolute;
	bottom: 100%;
	left: 50%;
	margin-bottom: 10px;
	transform: translateX(-50%);
	min-width: 250px;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
	background-color: #333;
	color: #fff;
	content: attr(data-wt-cli-tooltip);
	text-align: center;
	font-size: 12px;
	line-height: 18px;
	font-weight: normal;
	padding: 12px;
	letter-spacing: -.25px;
	text-transform: none;
	white-space: normal;
}

/* Triangle hack to make tooltip look like a speech bubble */

[data-wt-cli-tooltip]:after {
	position: absolute;
	bottom: 100%;
	left: 50%;
	margin-bottom: 5px;
	margin-left: -5px;
	width: 0;
	border-top: 5px solid #000;
	border-top: 5px solid hsla(0, 0%, 20%, 0.9);
	border-right: 5px solid transparent;
	border-left: 5px solid transparent;
	content: " ";
	font-size: 0;
	line-height: 0;
}

/* Show tooltip content on hover */

[data-wt-cli-tooltip]:hover:before, [data-wt-cli-tooltip]:hover:after {
	visibility: visible;
	opacity: 1;
}

span.wt-cli-tootip-icon {
	background-image: url('../images/wt-cli-info.svg');
}

span.wt-cli-tootip-icon {
	background-image: url(../images/wt-cli-info.svg);
	width: 14px;
	height: 14px;
	display: inline-flex;
	background-size: 100%;
	align-items: center;
	justify-content: center;
	margin-top: 5px;
	margin-left: 10px;
	margin-bottom: -2px;
}

.wt-cli-ccpa-message-toggler .wt-cli-form-group {
	margin-bottom: 15px;
}

.wt-cli-notice.wt-cli-info {
	padding: 15px 15px 15px 41px;
	background: #e5f5fa;
	position: relative;
	border-left: 4px solid;
	border-color: #00a0d2;
	margin-bottom: 15px;
	width: 100%;
	box-sizing: border-box;
}

.wt-cli-notice.wt-cli-info:before {
	content: "\f348";
	color: #00a0d2;
	font-family: "dashicons";
	position: absolute;
	left: 15px;
	font-size: 16px;
}

.wt-cli-status-success:before {
	content: "";
	background: url(../images/add.svg);
	width: 18px;
	height: 18px;
	display: block;
	background-size: cover;
}

.wt-cli-status-icon {
	width: 18px;
	height: 18px;
	font-size: 16px;
	text-align: center;
	content: "";
	font-family: "dashicons";
}

/* Toggle checkbox CSS since 1.9.4 */
.wt-cli-input-toggle-section {
	display: none;
}

.wt-cli-section-floating-widget-settings .wt-cli-input-toggle-section.wt-cli-toggle-active label {
	margin-left: 15px !important;
}

.wt-cli-section-floating-widget-settings .wt-cli-input-toggle-section label {
	-webkit-transition: margin 700ms;
	-moz-transition: margin 700ms;
	-o-transition: margin 700ms;
	transition: margin 700ms;
}

.wt-cli-section-floating-widget-settings .form-table th {
	width: 260px;
}


/* Callout styles  */

.wt-cli-callout {
	width: 100%;
	padding: 1.5em 1.5em 1.5em 1.5em;
	margin-bottom: 1.5em;
	overflow: auto;
	position: relative;
	border-width: 0 0 0 5px;
	border-style: solid;
	min-width: 170px;
	box-sizing: border-box;
}

.wt-cli-callout.wt-cli-callout-icon {
	padding: 1.5em 3em 1.6em 3.7em;
}

.wt-cli-callout p {
	margin-bottom: 0.6em;
	margin-top: 0.5em;
}

.wt-cli-callout p:first-child {
	margin-top: 0;
}

.wt-cli-callout p:last-child {
	margin-bottom: 0;
}

.wt-cli-callout:before {
	content: "";
	font-family: "dashicons";
	position: absolute;
	font-size: 20px;
	top: 50%;
	left: 0.5em;
	transform: translateY(-50%);
}

.wt-cli-callout .screen-reader-text+br {
	display: none;
}

.wt-cli-callout-info {
	background: #e5f5fa;
	border-color: #00a0d2;
}

.wt-cli-callout-success {
	background: #eff7ed;
	border-color: #64b450;
}

.wt-cli-callout-alert {
	background: #fff8e5;
	border-color: #ffb900;
}

.wt-cli-callout-tutorial {
	background: #f2f0f7;
	border-color: #826eb4;
}

.wt-cli-callout-warning {
	background: #fbeaea;
	border-color: #dc3232;
}

.wt-cli-callout-info.wt-cli-callout-icon:before {
	content: "\f348";
	color: #00a0d2;
}

.wt-cli-callout-success.wt-cli-callout-icon:before {
	content: "";
	color: #fff;
	width: 18px;
	height: 18px;
	background: url('../images/add.svg');
}

.wt-cli-callout-alert.wt-cli-callout-icon:before {
	content: "\f227";
	color: #ffb900;
}

.wt-cli-callout-tutorial.wt-cli-callout-icon:before {
	content: "\f308";
	color: #826eb4;
}

.wt-cli-callout-warning.wt-cli-callout-icon:before {
	content: "\f153";
	color: #dc3232;
}

/* Modal styles since 2.3.2 */

.wt-cli-modal {
	position: fixed;
	top: 10%;
	padding: 15px 20px;
	border-radius: 3px;
	background-color: white;
	z-index: 60000;
	width: 400px;
	display: none;
	left: 0;
	right: 0;
	margin: 0 auto;
	box-sizing: border-box;
}

#wt-cli-migrate-btn {
	cursor: pointer;
}

.wt-cky-migration-modal.wt-cli-modal {
	width: 700px;
	padding: 15px 20px;
}

.wt-cky-migration-modal .wt-cli-modal-js-close{
	padding-top: 15px;
	padding-right: 20px;
}

.wt-cky-migration-modal .wt-cli-modal-header h4 {
	line-height: 27px;
}

.wt-cky-migration-modal ul {
	padding-left: 20px;
}

.wt-cli-bullet-list, .wt-cli-bullet-list-main li{
	list-style-type: disc;
}

.wt-cky-migration-modal .wt-cli-modal-body p{
	padding-left: 20px;
}

.wt-cky-migration-modal .wt-cli-modal-body{
	padding-top: 16px;
}

.wt-cky-migration-modal .wt-cli-action-container{
	text-align: right;
}

.wt-cky-migration-modal .wt-cli-action-group{
	display: inline-flex;
	gap: 10px;
}

.wt-cky-migration-modal .wt-cli-action-container a{
	text-decoration: none;
	margin: 0;
	border-radius: 4px;
	padding: 8px 16px;
	font-weight: 600;
	line-height: 24px;
}

.wt-cky-migration-modal .wt-cli-action-container a:hover{
	transform: none;
}

.wt-cli-ckyes-support{
	border: 1px solid #2D9FFF;
	color: #2D9FFF;
	display: inline-flex;
	gap: 4px;
}

.wt-cli-modal.on {
	display: block;
}

.wt-cli-modal-js-overlay {
	background: #444;
	opacity: .8;
	position: fixed;
	top: 0px;
	width: 100%;
	height: 1000px;
	z-index: 20000;
	left: 0px;
}

.wt-cli-modal-js-close {
	position: absolute;
	bottom: 0px;
	right: 0px;
	color: #000;
	border-radius: 50%;
	font-size: 18px;
	text-align: center;
	padding: 1px;
	top: 5px;
	right: 5px;
	box-shadow: var(--box-shadow);
	cursor: pointer;
}

.wt-cli-modal-header h4 {
	margin: 0;
	font-size: 15px;
}

/* New grid styles */
.wt-cli-align-center {
	-webkit-box-align: center !important;
	-ms-flex-align: center !important;
	align-items: center !important;
}

.wt-cli-justify-center {
	-webkit-box-pack: justify !important;
	-ms-flex-pack: justify !important;
	justify-content: center !important;
}

.wt-cli-justify-end {
	-ms-flex-pack: end !important;
	justify-content: flex-end !important;
}

.wt-cli-container-fluid {
	width: 100%;
	padding-right: 15px;
	padding-left: 15px;
	margin-right: auto;
	margin-left: auto;
}

.wt-cli-grid-wrapper *, .wt-cli-row * {
	box-sizing: border-box;
}

.wt-cli-row {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	flex-wrap: wrap;
	-ms-flex-wrap: wrap;
	margin-left: -12.5px;
	margin-right: -12.5px;
}

.wt-cli-col {
	-ms-flex-preferred-size: 0;
	flex-basis: 0;
	-webkit-box-flex: 1;
	-ms-flex-positive: 1;
	flex-grow: 1;
	max-width: 100%;
}

.wt-cli-col-1 {
	-ms-flex: 0 0 8.33333333%;
	flex: 0 0 8.33333333%;
	max-width: 8.33333333%;
}

.wt-cli-col-2 {
	-ms-flex: 0 0 16.66666667%;
	flex: 0 0 16.66666667%;
	max-width: 16.66666667%;
}

.wt-cli-col-3 {
	-ms-flex: 0 0 25%;
	flex: 0 0 25%;
	max-width: 25%;
}

.wt-cli-col-4 {
	-ms-flex: 0 0 33.33333333%;
	flex: 0 0 33.33333333%;
	max-width: 33.33333333%;
}

.wt-cli-col-5 {
	-ms-flex: 0 0 41.66666667%;
	flex: 0 0 41.66666667%;
	max-width: 41.66666667%;
}

.wt-cli-col-6 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 50%;
	flex: 0 0 50%;
	max-width: 50%;
}

.wt-cli-col-7 {
	-ms-flex: 0 0 58.33333333%;
	flex: 0 0 58.33333333%;
	max-width: 58.33333333%;
}

.wt-cli-col-8 {
	-ms-flex: 0 0 66.66666667%;
	flex: 0 0 66.66666667%;
	max-width: 66.66666667%;
}

.wt-cli-col-9 {
	-ms-flex: 0 0 75%;
	flex: 0 0 75%;
	max-width: 75%;
}

.wt-cli-col-10 {
	-ms-flex: 0 0 83.33333333%;
	flex: 0 0 83.33333333%;
	max-width: 83.33333333%;
}

.wt-cli-col-11 {
	-ms-flex: 0 0 91.66666667%;
	flex: 0 0 91.66666667%;
	max-width: 91.66666667%;
}

.wt-cli-col-12 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 100%;
	flex: 0 0 100%;
	max-width: 100%;
}

.wt-cli-col, .wt-cli-col-1, .wt-cli-col-2, .wt-cli-col-3, .wt-cli-col-4, .wt-cli-col-5, .wt-cli-col-6, .wt-cli-col-7, .wt-cli-col-8, .wt-cli-col-9, .wt-cli-col-10, .wt-cli-col-11, .wt-cli-col-12 {
	position: relative;
	width: 100%;
	min-height: 1px;
	padding-right: 12.5px;
	padding-left: 12.5px;
}

.wt-cli-gdpr-plugin-status {
	display: flex;
	align-items: center;
	font-weight: 500;
}

.wt-cli-gdpr-plugin-status img {
	width: 18px;
	margin-right: 10px;
}

.wt-cli-gdpr-plugin-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.wt-cli-gdpr-plugin-branding-logo {
	display: flex;
	justify-content: flex-end;
}

.wt-cli-gdpr-plugin-branding-logo img {
	width: 150px;
}

.wt-cli-gdpr-plugin-branding-logo a:focus {
	outline: none;
	box-shadow: none;
}

.wt-cli-gdpr-plugin-branding-tagline a {
	color: #444;
}

.wt-cli-gdpr-plugin-branding-tagline {
	margin-top: 5px;
}

/* Upgrade side bar styles */
.wt-ier-sidebar-wrapper {
	background: #FFFFFF;
	border-radius: 5px;
	max-width: 370px;
}

.wt-ier-sidebar-img {
	margin: 0 auto 20px auto;
	display: block;
}

.wt-ier-sidebar-title {
	font-weight: 600;
	font-size: 17px;
	line-height: 25px;
	color: #000000;
	margin-bottom: 20px;
}

.wt-ier-icon {
	width: auto;
	height: 100%;
	max-height: 25px;
	margin-right: 8px;
}

.wt-ier-sidebar-p {
	font-style: normal;
	font-weight: normal;
	font-size: 12px;
	line-height: 16px;
	color: #000000;
	margin: 0;
}

.wt-ier-green-btn {
	background: #00CB95;
	border-radius: 4px;
	font-weight: 600;
	font-size: 16px;
	line-height: 19px;
	text-align: center;
	padding: 15px 20px;
	color: #FFFFFF;
	display: inline-block;
	text-decoration: none;
	transition: all .2s ease;
	border: none;
	margin-top: 20px;
}

.wt-ier-green-btn:hover, .wt-ier-green-btn:focus {
	text-decoration: none;
	opacity: .8;
	transition: all .2s ease;
	border: none;
	transform: translateY(1px);
}

.wt-ier-wrapper {
	background-color: #fff;
	padding: 30px;
}

.wt-ier-box-wrapper {
	background: #FFFFFF;
	border: 1px solid #D2D2D2;
	border-radius: 6px;
	min-height: 100%;
}

.wt-ier-p-5 {
	padding: 35px;
}

.wt-ier-p-4 {
	padding: 20px;
}

.wt-ier-v-center {
	-ms-flex-align: center;
	align-items: center;
}

.wt-ier-flex {
	display: -ms-flexbox;
	display: flex;
}

.wt-ier-center {
	text-align: center;
}

.wt-ier-row * {
	box-sizing: border-box;
}

.wt-ier-row {
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	margin: -15px;
}

.wt-ier-col-12 {
	-ms-flex: 0 0 100%;
	flex: 0 0 100%;
	max-width: 100%;

}

.wt-ier-col-md-6, .wt-ier-col-12 {
	position: relative;
	width: 100%;
	padding: 15px;
}

@media (min-width: 768px) {
	.wt-ier-col-md-6 {
		-ms-flex: 0 0 50%;
		flex: 0 0 50%;
		max-width: 50%;
	}

	.wt-ier-mb-md-0 {
		margin-bottom: 0;
	}

	.wt-ier-border-md-right {
		border-right: 1px solid #E8E8E8;
	}

}
.wt-cli-footer {
	padding: 15px;
	background: #f5f5f5;
	margin-left: -15px;
	margin-right: -15px;
	margin-bottom: -15px;
}
