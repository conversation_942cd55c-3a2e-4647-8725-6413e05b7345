(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-bac749f6"],{"76e6":function(t,e,s){"use strict";s.d(e,"a",(function(){return G}));var i,a=Symbol(),n=Symbol(),o=Symbol(),r=Symbol(),c=function(t){return"frag"in t},l={get:function(){return this[n]||this.parentElement},configurable:!0},d=function(t,e){n in t||(t[n]=e,Object.defineProperty(t,"parentNode",l))},u={get:function(){var t=this.parentNode.childNodes,e=t.indexOf(this);return e>-1&&t[e+1]||null}},g=function(t){o in t||(t[o]=!0,Object.defineProperty(t,"nextSibling",u))},p=function(t,e){while(t.parentNode!==e){var s=t,i=s.parentNode;i&&(t=i)}return t},f=function(t){if(!i){var e=Object.getOwnPropertyDescriptor(Node.prototype,"childNodes");i=e.get}var s=i.apply(t),a=Array.from(s).map((function(e){return p(e,t)}));return a.filter((function(t,e){return t!==a[e-1]}))},y={get:function(){return this.frag||f(this)}},h={get:function(){return this.childNodes[0]||null}};function _(){return this.childNodes.length>0}var v=function(t){r in t||(t[r]=!0,Object.defineProperties(t,{childNodes:y,firstChild:h}),t.hasChildNodes=_)};function k(){var t;(t=this.frag[0]).before.apply(t,arguments)}function m(){var t=this.frag,e=t.splice(0,t.length);e.forEach((function(t){t.remove()}))}var C=function t(e){var s;return(s=Array.prototype).concat.apply(s,e.map((function(e){return c(e)?t(e.frag):e})))},b=function(t,e){var s=t[a];e.before(s),d(s,t),t.frag.unshift(s)};function w(t){if(c(this)){var e=this.frag.indexOf(t);if(e>-1){var s=this.frag.splice(e,1),i=s[0];0===this.frag.length&&b(this,i),t.remove()}}else{var a=f(this),n=a.indexOf(t);n>-1&&t.remove()}return t}function $(t,e){var s=this,i=t.frag||[t];if(c(this)){if(t[n]===this&&t.parentElement)return t;var a=this.frag;if(e){var o=a.indexOf(e);o>-1&&(a.splice.apply(a,[o,0].concat(i)),e.before.apply(e,i))}else{var r=a[a.length-1];a.push.apply(a,i),r.after.apply(r,i)}S(this)}else e?this.childNodes.includes(e)&&e.before.apply(e,i):this.append.apply(this,i);i.forEach((function(t){d(t,s)}));var l=i[i.length-1];return g(l),t}function x(t){if(t[n]===this&&t.parentElement)return t;var e=this.frag,s=e[e.length-1];return s.after(t),d(t,this),S(this),e.push(t),t}var S=function(t){var e=t[a];t.frag[0]===e&&(t.frag.shift(),e.remove())},M={set:function(t){var e=this;if(this.frag[0]!==this[a]&&this.frag.slice().forEach((function(t){return e.removeChild(t)})),t){var s=document.createElement("div");s.innerHTML=t,Array.from(s.childNodes).forEach((function(t){e.appendChild(t)}))}},get:function(){return""}},N={inserted:function(t){var e=t.parentNode,s=t.nextSibling,i=t.previousSibling,n=Array.from(t.childNodes),o=document.createComment("");0===n.length&&n.push(o),t.frag=n,t[a]=o;var r=document.createDocumentFragment();r.append.apply(r,C(n)),t.replaceWith(r),n.forEach((function(e){d(e,t),g(e)})),v(t),Object.assign(t,{remove:m,appendChild:x,insertBefore:$,removeChild:w,before:k}),Object.defineProperty(t,"innerHTML",M),e&&(Object.assign(e,{removeChild:w,insertBefore:$}),d(t,e),v(e)),s&&g(t),i&&g(i)},unbind:function(t){t.remove()}},G={name:"Fragment",directives:{frag:N},render:function(t){return t("div",{directives:[{name:"frag"}]},this.$slots["default"])}}},"955e":function(t,e,s){"use strict";s.r(e);var i=function(){var t=this,e=t._self._c;return e("fragment",[e("div",{staticClass:"cky-section cky-zero-padding cky-zero--margin"},[e("div",{staticClass:"cky-section-header cky-align-center cky-justify-between"},[e("div",{staticClass:"cky-section-title"},[e("h3",[t._v(t._s(t.$i18n.__("Google Consent Mode Settings","cookie-law-info")))])]),e("div",{staticClass:"cky-section-header-actions cky-align-center"},[e("div",{staticClass:"cky-align-center cky-justify-end"},[e("cky-button",{ref:"ckyButtonSaveBanner",staticClass:"cky-button cky-button-green",staticStyle:{"margin-left":"15px"},attrs:{disabled:t.publishDisabled},nativeOn:{click:function(e){return t.saveConfig.apply(null,arguments)}}},[t._v(" "+t._s(t.$i18n.__("Publish Changes","cookie-law-info"))+" ")])],1)])]),e("div",{staticClass:"cky-section-content"},[e("div",{staticClass:"cky-col-12 cky-gcm-settings"},[e("div",{staticClass:"cky-app-box"},[e("div",{staticClass:"cky-form-section cky-row"},[e("div",{staticClass:"cky-col-3"},[e("label",{staticClass:"cky-zero-padding cky-col-label",attrs:{for:"cky-toggle-gcm"}},[e("h6",[t._v(" "+t._s(t.$i18n.__("Enable Google Consent Mode (GCM)","cookie-law-info"))+" ")])])]),e("div",{staticClass:"cky-col-9 cky-align-center"},[e("label",{staticClass:"cky-toggle",attrs:{for:"cky-toggle-gcm"}},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.settings.status,expression:"settings.status"}],attrs:{type:"checkbox",id:"cky-toggle-gcm"},domProps:{checked:Array.isArray(t.settings.status)?t._i(t.settings.status,null)>-1:t.settings.status},on:{change:[function(e){var s=t.settings.status,i=e.target,a=!!i.checked;if(Array.isArray(s)){var n=null,o=t._i(s,n);i.checked?o<0&&t.$set(t.settings,"status",s.concat([n])):o>-1&&t.$set(t.settings,"status",s.slice(0,o).concat(s.slice(o+1)))}else t.$set(t.settings,"status",a)},function(e){return t.handleStatusChange(t.settings.status)}]}}),e("span",{staticClass:"cky-toggle-slider",attrs:{id:"cky-toggle-gcm-toggle","aria-hidden":"true"}})])]),e("div",{staticClass:"cky-col-3"}),e("div",{staticClass:"cky-col-9 cky-tooltip-text"},[e("p",[t._v(" "+t._s(t.$i18n.__("When enabled, GCM will be implemented on your website.","cookie-law-info"))+" ")])])]),e("div",{staticClass:"cky-row"},[e("div",{staticClass:"cky-col-12 cky-col-label"},[e("h6",[t._v(" "+t._s(t.$i18n.__("Default consent settings","cookie-law-info"))+" ")])]),e("div",{staticClass:"cky-col-12 cky-tooltip-text"},[e("p",[t._v(" "+t._s(t.$i18n.__("The default consent state, ‘Denied’, will apply to non-necessary categories until consent is received. You can customise the default consent states for users in different geographical regions.","cookie-law-info"))+" ")])])]),e("div",{staticClass:"cky-form-section cky-row"},[e("div",{staticClass:"cky-col-12"},[e("div",{staticClass:"cky-default-consent-lists"},[e("table",{staticClass:"wp-list-table cky-table cky-consent-table"},[e("thead",[e("tr",[e("th",[t._v(t._s(t.$i18n.__("Analytics","cookie-law-info")))]),e("th",[t._v(t._s(t.$i18n.__("Advertisement","cookie-law-info")))]),e("th",[t._v(t._s(t.$i18n.__("Functional","cookie-law-info")))]),e("th",[t._v(t._s(t.$i18n.__("Necessary","cookie-law-info")))]),e("th",[t._v(t._s(t.$i18n.__("Share user data with Google","cookie-law-info")))]),e("th",[t._v(t._s(t.$i18n.__("Use data for ads personalisation","cookie-law-info")))]),e("th",[t._v(t._s(t.$i18n.__("Region","cookie-law-info")))]),e("th",[t._v(t._s(t.$i18n.__("Actions","cookie-law-info")))])])]),e("tbody",t._l(t.defaultSettings,(function(s,i){return e("tr",{key:i},[e("td",[t._v(t._s(t.mappedValue(s.analytics)))]),e("td",[t._v(t._s(t.mappedValue(s.advertisement)))]),e("td",[t._v(t._s(t.mappedValue(s.functional)))]),e("td",[t._v(t._s(t.mappedValue(s.necessary)))]),e("td",[t._v(t._s(t.mappedValue(s.ad_user_data)))]),e("td",[t._v(t._s(t.mappedValue(s.ad_personalization)))]),e("td",[t._v(t._s(t.regionMappedVal(s.regions)))]),e("td",{staticClass:"cky-consent-table-col-actions"},[e("button",{staticClass:"cky-button-no-style",on:{click:function(e){return t.openConsentModal(s,i)}}},[e("cky-popper",{attrs:{content:t.$i18n.__("Edit","cookie-law-info")}},[e("cky-icon",{attrs:{width:"14px",icon:"edit",color:"#505B66"}})],1)],1),t.isDeleteDisabled?e("button",{staticClass:"cky-button-no-style"},[e("cky-popper",{attrs:{content:t.$i18n.__("This row can’t be deleted as it specifies the default values.","cookie-law-info")}},[e("cky-icon",{attrs:{width:"14px",icon:"trash",color:"#DE443780"}})],1)],1):e("button",{staticClass:"cky-button-no-style",on:{click:function(e){return t.deleteRegion(i)}}},[e("cky-popper",{attrs:{content:t.$i18n.__("Delete","cookie-law-info")}},[e("cky-icon",{attrs:{width:"14px",icon:"trash",color:"#DE4437"}})],1)],1)])])})),0)])])]),e("div",{staticClass:"cky-col-2"},[e("button",{staticClass:"cky-button",attrs:{id:"cky-add-new-region-btn"},on:{click:function(e){return t.openConsentModal(t.defaultItem)}}},[t._v(" + "+t._s(t.$i18n.__("New Region","cookie-law-info"))+" ")])])]),e("div",{staticClass:"cky-row cky-other-settings"},[e("div",{staticClass:"cky-col-12 cky-col-label"},[e("h6",[t._v(" "+t._s(t.$i18n.__("Other settings","cookie-law-info"))+" ")])]),e("label",{staticClass:"cky-col-3 cky-col-label cky-align-center",attrs:{for:"cky-wait-update"}},[t._v(" "+t._s(t.$i18n.__("Wait for update","cookie-law-info"))+" ")]),e("div",{staticClass:"cky-col-9"},[e("div",{staticClass:"cky-wait-update cky-align-center"},[e("cky-wait-update"),e("p",[t._v(" "+t._s(t.$i18n.__("milliseconds","cookie-law-info"))+" ")])],1)]),e("div",{staticClass:"cky-col-3"}),e("div",{staticClass:"cky-col-9 cky-tooltip-text"},[e("p",[t._v(" "+t._s(t.$i18n.__("Number of milliseconds to wait before firing tags that are waiting for consent.","cookie-law-info"))+" ")])]),e("div",{staticClass:"cky-col-3"},[e("label",{staticClass:"cky-zero-padding cky-col-label",attrs:{for:"cky-toggle-url-passthrough"}},[t._v(" "+t._s(t.$i18n.__("Pass ad click information through URLs","cookie-law-info"))+" ")])]),e("div",{staticClass:"cky-col-9 cky-align-center"},[e("label",{staticClass:"cky-toggle",attrs:{for:"cky-toggle-url-passthrough"}},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.settings.url_passthrough,expression:"settings.url_passthrough"}],attrs:{type:"checkbox",id:"cky-toggle-url-passthrough"},domProps:{checked:Array.isArray(t.settings.url_passthrough)?t._i(t.settings.url_passthrough,null)>-1:t.settings.url_passthrough},on:{change:function(e){var s=t.settings.url_passthrough,i=e.target,a=!!i.checked;if(Array.isArray(s)){var n=null,o=t._i(s,n);i.checked?o<0&&t.$set(t.settings,"url_passthrough",s.concat([n])):o>-1&&t.$set(t.settings,"url_passthrough",s.slice(0,o).concat(s.slice(o+1)))}else t.$set(t.settings,"url_passthrough",a)}}}),e("span",{staticClass:"cky-toggle-slider",attrs:{id:"cky-toggle-url-passthrough-toggle","aria-hidden":"true"}})])]),e("div",{staticClass:"cky-col-3"}),e("div",{staticClass:"cky-col-9 cky-tooltip-text"},[e("p",[t._v(" "+t._s(t.$i18n.__("When enabled, internal links will include advertising identifiers (such as gclid, dclid, gclsrc, and _gl) in their URLs while awaiting consent.","cookie-law-info"))+" ")])]),e("div",{staticClass:"cky-col-3"},[e("label",{staticClass:"cky-zero-padding cky-col-label",attrs:{for:"cky-toggle-ads-redact"}},[t._v(" "+t._s(t.$i18n.__("Redact ads data","cookie-law-info"))+" ")])]),e("div",{staticClass:"cky-col-9 cky-align-center"},[e("label",{staticClass:"cky-toggle",attrs:{for:"cky-toggle-ads-redact"}},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.settings.ads_data_redaction,expression:"settings.ads_data_redaction"}],attrs:{type:"checkbox",id:"cky-toggle-ads-redact"},domProps:{checked:Array.isArray(t.settings.ads_data_redaction)?t._i(t.settings.ads_data_redaction,null)>-1:t.settings.ads_data_redaction},on:{change:function(e){var s=t.settings.ads_data_redaction,i=e.target,a=!!i.checked;if(Array.isArray(s)){var n=null,o=t._i(s,n);i.checked?o<0&&t.$set(t.settings,"ads_data_redaction",s.concat([n])):o>-1&&t.$set(t.settings,"ads_data_redaction",s.slice(0,o).concat(s.slice(o+1)))}else t.$set(t.settings,"ads_data_redaction",a)}}}),e("span",{staticClass:"cky-toggle-slider",attrs:{id:"cky-toggle-ads-redact-toggle","aria-hidden":"true"}})])]),e("div",{staticClass:"cky-col-3"}),e("div",{staticClass:"cky-col-9 cky-tooltip-text"},[e("p",[t._v(" "+t._s(t.$i18n.__('When enabled and the default consent state of "Advertisement Cookies" is disabled, Google\'s advertising tags will remove all advertising identifiers from the requests, and route the traffic through domains that do not use cookies.',"cookie-law-info"))+" ")])])])])])])]),e("gcm-headsup-modal",{ref:"ckyGcmHeadsUpModal"}),e("consent-setting-modal",{ref:"ckyConsentSettingModal",on:{clear:t.clearConsentSettingModal}})],1)},a=[],n=s("a2b6"),o=function(){var t=this,e=t._self._c;return e("cky-input",{class:{"cky-input-error":t.hasErrors},attrs:{type:"number",inputId:t.inputId,min:0},on:{input:e=>t.checkForErrors(e)},model:{value:t.settings.wait_for_update,callback:function(e){t.$set(t.settings,"wait_for_update",t._n(e))},expression:"settings.wait_for_update"}},[t.hasErrors?e("template",{slot:"cky-input-error"},[e("div",{staticClass:"cky-input-error-container"},t._l(t.errors,(function(s,i){return e("div",{key:i,staticClass:"cky-input-inline-error"},[!0===s?[t._v(t._s(t.errorMessages[i]))]:t._e()],2)})),0)]):t._e()],2)},r=[],c=s("c4aa"),l={name:"CkyWaitUpdate",components:{},data(){return{errors:{},errorMessages:{maxLimit:this.$i18n.__("The value must be a positive integer.","cookie-law-info")},inputId:"cky-wait-update"}},methods:{checkForErrors(t){this.errors={},(""===t||t<0)&&(this.errors["maxLimit"]=!0),c["a"].setErrors(this.errors)}},computed:{settings(){return this.$store.state.settings.gcm},hasErrors(){return Object.keys(this.errors).length},getValue(){return this.settings.wait_for_update}},watch:{getValue(t){this.checkForErrors(t)}}},d=l,u=s("2877"),g=Object(u["a"])(d,o,r,!1,null,null,null),p=g.exports,f=s("1f3d"),y=s("76e6"),h=s("f9c4"),_={async createGcm(t){try{const e=await h["a"].post({path:"gcm/",data:t});return e}catch(e){console.error(e)}}},v=function(){var t=this,e=t._self._c;return e("cky-modal",{ref:"ckyGcmHeadsUpModal",staticClass:"cky-gcm-headsup-modal",on:{close:t.changeGcmStatus},scopedSlots:t._u([{key:"header",fn:function(){return[e("h4",[t._v(" "+t._s(t.$i18n.__("Heads up!","cookie-law-info"))+" ")])]},proxy:!0},{key:"body",fn:function(){return[e("p",[t._v(t._s(t.$i18n.__("Please ensure that you’ve NOT manually added any custom scripts for implementing Google Consent Mode (GCM) on your site. If such scripts are present, remove them before enabling GCM here.","cookie-law-info"))+" ")])]},proxy:!0},{key:"footer",fn:function(){return[e("div",{staticClass:"cky-app-modal-actions cky-justify-end"},[e("button",{staticClass:"cky-button cky-button-outline-secondary",on:{click:t.closeGcmHeadsupModal}},[t._v(" "+t._s(t.$i18n.__("Cancel","cookie-law-info"))+" ")]),e("cky-button",{staticClass:"cky-button-primary",nativeOn:{click:function(e){return t.enableGcm.apply(null,arguments)}}},[t._v(" "+t._s(t.$i18n.__("Enable GCM","cookie-law-info"))+" ")])],1)]},proxy:!0}])})},k=[],m=s("8a80"),C={name:"GcmHeadsupModal",components:{CkyModal:m["a"]},computed:{settings(){return this.$store.state.settings.gcm}},methods:{changeGcmStatus(t=!1){this.settings.status=t},closeGcmHeadsupModal(){this.changeGcmStatus(),this.$refs.ckyGcmHeadsUpModal.close()},enableGcm(){this.$refs.ckyGcmHeadsUpModal.close(),this.changeGcmStatus(!0)},show(){this.$refs.ckyGcmHeadsUpModal.show()}}},b=C,w=Object(u["a"])(b,v,k,!1,null,null,null),$=w.exports,x=function(){var t=this,e=t._self._c;return e("cky-modal",{ref:"ckyConsentSettingModal",on:{close:t.clearConsentSettingModal},scopedSlots:t._u([{key:"header",fn:function(){return[null!==t.itemIndex?e("h4",[t._v(" "+t._s(t.$i18n.__("Edit Region","cookie-law-info"))+" ")]):e("h4",[t._v(" "+t._s(t.$i18n.__("New Region","cookie-law-info"))+" ")])]},proxy:!0},{key:"body",fn:function(){return[e("div",{staticClass:"cky-row"},[e("div",{staticClass:"cky-col-6"},[e("div",{staticClass:"cky-form-group"},[e("label",{staticClass:"cky-label"},[t._v(t._s(t.$i18n.__("Analytics","cookie-law-info"))+" ")]),e("select",{directives:[{name:"model",rawName:"v-model",value:t.item.analytics,expression:"item.analytics"}],staticClass:"cky-select",on:{change:function(e){var s=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.$set(t.item,"analytics",e.target.multiple?s:s[0])}}},t._l(t.values,(function(s){return e("option",{key:s.index,domProps:{value:s.index}},[t._v(" "+t._s(s.value)+" ")])})),0)])]),e("div",{staticClass:"cky-col-6"},[e("div",{staticClass:"cky-form-group"},[e("label",{staticClass:"cky-label"},[t._v(t._s(t.$i18n.__("Advertisement","cookie-law-info"))+" ")]),e("select",{directives:[{name:"model",rawName:"v-model",value:t.item.advertisement,expression:"item.advertisement"}],staticClass:"cky-select",on:{change:function(e){var s=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.$set(t.item,"advertisement",e.target.multiple?s:s[0])}}},t._l(t.values,(function(s){return e("option",{key:s.index,domProps:{value:s.index}},[t._v(" "+t._s(s.value)+" ")])})),0)])]),e("div",{staticClass:"cky-col-6"},[e("div",{staticClass:"cky-form-group"},[e("label",{staticClass:"cky-label"},[t._v(t._s(t.$i18n.__("Functional","cookie-law-info"))+" ")]),e("select",{directives:[{name:"model",rawName:"v-model",value:t.item.functional,expression:"item.functional"}],staticClass:"cky-select",on:{change:function(e){var s=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.$set(t.item,"functional",e.target.multiple?s:s[0])}}},t._l(t.values,(function(s){return e("option",{key:s.index,domProps:{value:s.index}},[t._v(" "+t._s(s.value)+" ")])})),0)])]),e("div",{staticClass:"cky-col-6"},[e("div",{staticClass:"cky-form-group"},[e("label",{staticClass:"cky-label"},[t._v(t._s(t.$i18n.__("Necessary","cookie-law-info"))+" ")]),e("select",{directives:[{name:"model",rawName:"v-model",value:t.item.necessary,expression:"item.necessary"}],staticClass:"cky-select",on:{change:function(e){var s=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.$set(t.item,"necessary",e.target.multiple?s:s[0])}}},t._l(t.values,(function(s){return e("option",{key:s.index,domProps:{value:s.index}},[t._v(" "+t._s(s.value)+" ")])})),0)])]),e("div",{staticClass:"cky-col-6"},[e("div",{staticClass:"cky-form-group"},[e("label",{staticClass:"cky-label"},[t._v(t._s(t.$i18n.__("Share user data with Google","cookie-law-info"))+" ")]),e("select",{directives:[{name:"model",rawName:"v-model",value:t.item.ad_user_data,expression:"item.ad_user_data"}],staticClass:"cky-select",on:{change:function(e){var s=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.$set(t.item,"ad_user_data",e.target.multiple?s:s[0])}}},t._l(t.values,(function(s){return e("option",{key:s.index,domProps:{value:s.index}},[t._v(" "+t._s(s.value)+" ")])})),0)])]),e("div",{staticClass:"cky-col-6"},[e("div",{staticClass:"cky-form-group"},[e("label",{staticClass:"cky-label"},[t._v(t._s(t.$i18n.__("Use data for ads personalisation","cookie-law-info"))+" ")]),e("select",{directives:[{name:"model",rawName:"v-model",value:t.item.ad_personalization,expression:"item.ad_personalization"}],staticClass:"cky-select",on:{change:function(e){var s=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.$set(t.item,"ad_personalization",e.target.multiple?s:s[0])}}},t._l(t.values,(function(s){return e("option",{key:s.index,domProps:{value:s.index}},[t._v(" "+t._s(s.value)+" ")])})),0)])]),e("div",{staticClass:"cky-col-12"},[e("div",{staticClass:"cky-form-group"},[e("label",{staticClass:"cky-label"},[t._v(t._s(t.$i18n.__("Region","cookie-law-info"))+" ")]),e("input",{directives:[{name:"model",rawName:"v-model",value:t.item.regions,expression:"item.regions"}],staticClass:"cky-form-control",attrs:{type:"text",required:""},domProps:{value:t.item.regions},on:{input:function(e){e.target.composing||t.$set(t.item,"regions",e.target.value)}}})])]),e("div",{staticClass:"cky-col-12 cky-tooltip-text"},[e("p",{domProps:{innerHTML:t._s(t.tooltip.regions)}})])])]},proxy:!0},{key:"footer",fn:function(){return[e("div",{staticClass:"cky-app-modal-actions cky-justify-end"},[e("button",{staticClass:"cky-button cky-button-outline-secondary",on:{click:t.closeConsentSettingModal}},[t._v(" "+t._s(t.$i18n.__("Cancel","cookie-law-info"))+" ")]),e("cky-button",{ref:"ckyButtonCreateEditRegion",staticClass:"cky-button-primary",nativeOn:{click:function(e){return t.addOrEditRegion.apply(null,arguments)}}},[t._v(" "+t._s(t.$i18n.__("Save Changes","cookie-law-info"))+" ")])],1)]},proxy:!0}])})},S=[],M=s("2b0e"),N={name:"ConsentSettingModal",components:{CkyModal:m["a"]},data(){return{item:{},itemIndex:null,values:[{index:"denied",value:this.$i18n.__("Denied","cookie-law-info")},{index:"granted",value:this.$i18n.__("Granted","cookie-law-info")}],tooltip:{regions:this.$i18n.sprintf(this.$i18n.__('By specifying “All”, consent will apply to all regions. You can specify a comma-separated list of <a href="%1$s" target="_blank">regions</a> to apply consent to specific regions.',"cookie-law-info"),"https://en.wikipedia.org/wiki/ISO_3166-2")}}},computed:{settings(){return this.$store.state.settings.gcm},defaultSettings(){return this.settings.default_settings}},methods:{clearConsentSettingModal(){this.$emit("clear")},closeConsentSettingModal(){this.$refs.ckyConsentSettingModal.close(),this.clearConsentSettingModal()},show(t,e){this.item=t,this.itemIndex=e,this.$refs.ckyConsentSettingModal.show()},async addOrEditRegion(){const t=this.itemIndex;let e=this.defaultSettings;null===t?e.push(this.item):M["a"].set(e,t,this.item),await this.$store.dispatch("settings/setGcmInfo",this.settings),this.closeConsentSettingModal()}}},G=N,O=Object(u["a"])(G,x,S,!1,null,null,null),A=O.exports,E=s("63ea"),P=s.n(E),j={name:"Gcm",components:{CkyIcon:f["a"],CkyWaitUpdate:p,Fragment:y["a"],GcmHeadsupModal:$,ConsentSettingModal:A},data(){return{item:{},itemIndex:null,defaultItem:{analytics:"denied",advertisement:"denied",functional:"denied",necessary:"granted",ad_user_data:"denied",ad_personalization:"denied",regions:"All"},initialStoreState:null}},computed:{settings(){return this.$store.state.settings.gcm},defaultSettings(){return this.settings.default_settings},isDeleteDisabled(){return 1==this.defaultSettings.length},publishDisabled(){return c["a"].hasErrors()}},methods:{hasChanges(){return!P()(this.initialStoreState,this.settings)},handleBeforeUnload(t){this.hasChanges()&&(t.preventDefault(),t.returnValue="")},resetChanges(){this.$store.state.settings.gcm=JSON.parse(JSON.stringify(this.initialStoreState))},clearConsentSettingModal(){this.item={},this.itemIndex=null},mappedValue(t){return"granted"===t?"Granted":"Denied"},regionMappedVal(t){return t.split(",").length>3?t.split(",",3).join(",")+" & more":t},capitaliseString(t){return t.toUpperCase()},openConsentModal(t={},e=null){this.item=Object(n["a"])(t),this.itemIndex=e,this.$refs.ckyConsentSettingModal.show(this.item,this.itemIndex)},deleteRegion(t=null){if(confirm(this.$i18n.__("Are you sure you want to delete this region?","cookie-law-info"))){let e=this.defaultSettings;null!==t&&e.splice(t,1)}},async saveConfig(){this.$refs.ckyButtonSaveBanner.startLoading();try{let t={};const e=this.settings;t=await _.createGcm(e),t&&this.$root.$emit("triggerNotification",{type:"success",message:this.$i18n.__("Successfully updated","cookie-law-info")})}catch(t){this.$root.$emit("triggerNotification",{type:"error",message:this.$i18n.__("Problem occurred while saving your settings. Please try again later!","cookie-law-info")})}this.$refs.ckyButtonSaveBanner.stopLoading(),this.initialStoreState=JSON.parse(JSON.stringify(this.settings))},handleStatusChange(t){!0===t&&this.$refs.ckyGcmHeadsUpModal.show()}},mounted(){this.initialStoreState=JSON.parse(JSON.stringify(this.settings)),window.addEventListener("beforeunload",this.handleBeforeUnload)},beforeDestroy(){window.removeEventListener("beforeunload",this.handleBeforeUnload)}},D=j,I=Object(u["a"])(D,i,a,!1,null,null,null);e["default"]=I.exports}}]);