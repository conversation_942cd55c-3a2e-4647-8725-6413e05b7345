/******/ (function() { // webpackBootstrap
/*!*******************************************************!*\
  !*** ./assets/js/frontend/components/collab-popup.js ***!
  \*******************************************************/
/* ============================================================
 * Collab Popup Component
 * Manages the state and behavior of collaboration popup
 * ============================================================ */

const CollabPopup = function ($) {
  'use strict';

  // Constants
  const STORAGE_KEY = 'collab-popup-state';
  const STATES = {
    MAXIMIZE: 'maximize',
    MINIMIZE: 'minimize'
  };
  const SELECTORS = {
    TOGGLE_BUTTON: '.collab__toggle',
    SHORT_POPUP: '.collab-short',
    FULL_POPUP: '.collab-full'
  };
  const EVENTS = {
    POPUP_TOGGLE: 'collab-popup-toggle'
  };
  const CSS_CLASSES = {
    HIDE: 'hide',
    SHOW: 'show'
  };

  /**
   * Get the current popup state from localStorage
   * @returns {string|null} Current state or null if not set
   */
  function getPopupState() {
    return localStorage.getItem(STORAGE_KEY);
  }

  /**
   * Set the popup state in localStorage
   * @param {string} state - The state to save
   */
  function setPopupState(state) {
    localStorage.setItem(STORAGE_KEY, state);
  }

  /**
   * Toggle the popup state between maximize and minimize
   */
  function togglePopupState() {
    const currentState = getPopupState();
    const newState = currentState === STATES.MAXIMIZE ? STATES.MINIMIZE : STATES.MAXIMIZE;
    setPopupState(newState);

    // Trigger custom event with the previous state
    $(document).trigger(EVENTS.POPUP_TOGGLE, [newState]);
  }

  /**
   * Show the full popup and hide the short version
   */
  function showFullPopup() {
    $(SELECTORS.SHORT_POPUP).addClass(CSS_CLASSES.HIDE);
    $(SELECTORS.FULL_POPUP).addClass(CSS_CLASSES.SHOW);
  }

  /**
   * Show the short popup and hide the full version
   */
  function showShortPopup() {
    $(SELECTORS.FULL_POPUP).removeClass(CSS_CLASSES.SHOW);
    $(SELECTORS.SHORT_POPUP).removeClass(CSS_CLASSES.HIDE);
  }

  /**
   * Handle popup state changes
   * @param {string} newState - The state before the toggle
   */
  function handleStateChange(newState) {
    if (newState === STATES.MAXIMIZE) {
      showFullPopup();
    } else {
      showShortPopup();
    }
  }

  /**
   * Initialize the popup with default state if none exists
   */
  function initializeDefaultState() {
    const currentState = getPopupState();
    if (!currentState) {
      showFullPopup();
      setPopupState(STATES.MAXIMIZE);
    } else {
      setPopupState(STATES.MINIMIZE);
    }
  }

  /**
   * Bind event handlers
   */
  function bindEvents() {
    // Handle toggle button clicks
    $(document).on('click', SELECTORS.TOGGLE_BUTTON, togglePopupState);

    // Handle custom popup toggle events
    $(document).on(EVENTS.POPUP_TOGGLE, function (event, newState) {
      handleStateChange(newState);
    });
  }

  /**
   * Initialize the collab popup component
   */
  function init() {
    bindEvents();
    initializeDefaultState();
  }

  // Public API
  return {
    init: init,
    toggle: togglePopupState,
    getState: getPopupState,
    setState: setPopupState
  };
}(jQuery);

// Initialize when DOM is ready
jQuery(document).ready(function () {
  CollabPopup.init();
});
/******/ })()
;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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