!function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=2)}({2:function(e,t){const n={gdpr:"optin",ccpa:"optout"},o={functional:"preferences",analytics:["statistics","statistics-anonymous"],performance:"functional",advertisement:"marketing"},r=!("undefined"==typeof _ckyGsk||!_ckyGsk)&&_ckyGsk;document.addEventListener("cookieyes_consent_update",(function(){const e=getCkyConsent(),t=e.categories;if(!1===e.isUserActionCompleted&&r&&!Object.values(t).slice(1).includes(!0))return;window.wp_consent_type=e.activeLaw?n[e.activeLaw]:"optin";let c=new CustomEvent("wp_consent_type_defined");document.dispatchEvent(c),Object.entries(t).forEach(([e,t])=>{e in o&&function(e,t){Array.isArray(o[e])?o[e].forEach(e=>{wp_set_consent(e,t)}):wp_set_consent(o[e],t)}(e,t?"allow":"deny")})}))}});