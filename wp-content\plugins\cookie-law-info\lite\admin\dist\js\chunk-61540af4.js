(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-61540af4"],{"1dc8":function(i,c,t){i.exports=t.p+"img/privacy-policy.svg"},"275f":function(i,c,t){},"281e":function(i,c,t){"use strict";t.r(c);var o=function(){var i=this,c=i._self._c;return c("div",{staticClass:"cky-section cky-section-policies cky-zero-padding cky-zero--margin"},[c("div",{staticClass:"cky-row"},[c("div",{staticClass:"cky-col-12"},[c("cky-connect-success")],1)]),i._m(0),c("div",{staticClass:"cky-section-content"},[c("div",{staticClass:"cky-row"},[c("div",{staticClass:"cky-card cky-card-policy"},[c("div",{staticClass:"cky-policy-icon cky-align-center"},[c("cky-icon",{attrs:{icon:"privacy-policy",width:"56px",height:"56px",color:"#8893a1"}})],1),c("div",{staticClass:"cky-card-title"},[c("h3",[i._v(" "+i._s(i.$i18n.__("Privacy Policy Generator","cookie-law-info"))+" ")])]),c("div",{staticClass:"cky-policy-description"},[c("p",[i._v(" "+i._s(i.$i18n.__("Create a privacy policy to inform users about the data collection practices of your website.","cookie-law-info"))+" ")])]),c("ul",{staticClass:"cky-policy-features"},i._l(i.privacyPolicyFeatures,(function(t){return c("li",{key:t},[i._m(1,!0),i._v(" "+i._s(i.$i18n.__(t,"cookie-law-info"))+" ")])})),0),c("div",{staticClass:"cky-policy-btn"},[c("a",{staticClass:"cky-button cky-button-primary cky-button-medium",on:{click:i.showPrivacyPolicy}},[i._v(" "+i._s(i.$i18n.__("Connect to Web App to Generate","cookie-law-info"))+" ")])]),c("cky-connect-modal",{ref:"ckyConnectModal",scopedSlots:i._u([{key:"title",fn:function(){return[c("img",{attrs:{src:t("1dc8"),alt:"privacy policy"}})]},proxy:!0},{key:"message",fn:function(){return[c("div",{staticClass:"cky-feature-text"},[i._v(" "+i._s(i.$i18n.__("Generate a custom privacy policy for your site in just minutes","cookie-law-info"))+" ")]),c("div",{staticClass:"cky-available-wrapper"},[c("span",{staticClass:"cky-available-text",domProps:{innerHTML:i._s(i.$i18n.__("Available in: <b>All plans</b>","cookie-law-info"))}})])]},proxy:!0}])})],1),c("div",{staticClass:"cky-card cky-card-policy"},[c("div",{staticClass:"cky-policy-icon cky-align-center"},[c("cky-icon",{attrs:{icon:"cookie-policy",width:"56px",height:"56px",color:"#8893a1"}})],1),c("div",{staticClass:"cky-card-title"},[c("h3",[i._v(" "+i._s(i.$i18n.__("Cookie Policy Generator","cookie-law-info"))+" ")])]),c("div",{staticClass:"cky-policy-description"},[c("p",[i._v(" "+i._s(i.$i18n.__("Generate a custom cookie policy and inform users about your site's use of cookies.","cookie-law-info"))+" ")])]),c("ul",{staticClass:"cky-policy-features"},i._l(i.cookiePolicyFeatures,(function(t){return c("li",{key:t},[i._m(2,!0),i._v(" "+i._s(i.$i18n.__(t,"cookie-law-info"))+" ")])})),0),c("div",{staticClass:"cky-policy-btn"},[c("a",{staticClass:"cky-button cky-button-primary cky-button-medium",on:{click:i.showCookiePolicy}},[i._v(" "+i._s(i.$i18n.__("Connect to Web App to Generate","cookie-law-info"))+" ")])]),c("cky-connect-modal",{ref:"ckyCookiePolicy",scopedSlots:i._u([{key:"title",fn:function(){return[c("img",{attrs:{src:t("b8f6"),alt:"cookie policy"}})]},proxy:!0},{key:"message",fn:function(){return[c("div",{staticClass:"cky-feature-text"},[i._v(" "+i._s(i.$i18n.__("Generate an auto-updating cookie policy for your website","cookie-law-info"))+" ")]),c("div",{staticClass:"cky-available-wrapper"},[c("span",{staticClass:"cky-available-text",domProps:{innerHTML:i._s(i.$i18n.__("Available in: <b>All plans</b>","cookie-law-info"))}})])]},proxy:!0}])})],1)])])])},s=[function(){var i=this,c=i._self._c;return c("div",{staticClass:"cky-section-header cky-align-center cky-justify-between"},[c("div",{staticClass:"cky-section-header-actions cky-align-center"})])},function(){var i=this,c=i._self._c;return c("span",{staticClass:"cky-icon-tiny"},[c("img",{attrs:{src:t("f222")}})])},function(){var i=this,c=i._self._c;return c("span",{staticClass:"cky-icon-tiny"},[c("img",{attrs:{src:t("f222")}})])}],e=t("1f3d"),a=t("c068"),n=t("2f62"),r=t("919d"),l=t("9947"),y={name:"CkyPolicies",mixins:[a["a"]],components:{CkyIcon:e["a"],CkyConnectSuccess:r["a"],CkyConnectModal:l["a"]},data(){return{privacyPolicyFeatures:["Answer a simple questionnaire","Generate policy in minutes","Copy as text/HTML","Customise as required"],cookiePolicyFeatures:["Instantly generate custom policy","Auto-updating cookie list","Copy as text/HTML","Customise as required"]}},methods:{showPrivacyPolicy(){this.$refs.ckyConnectModal.show()},showCookiePolicy(){this.$refs.ckyCookiePolicy.show()}},computed:{...Object(n["d"])("settings",["options"]),connected(){return this.options.account.connected&&this.options.account.connected||!1},currentTabComponent:function(){return"tab-"+this.currentTab.toLowerCase()}}},k=y,u=(t("433f"),t("2877")),p=Object(u["a"])(k,o,s,!1,null,null,null);c["default"]=p.exports},"433f":function(i,c,t){"use strict";t("275f")},b8f6:function(i,c,t){i.exports=t.p+"img/cookie-policy.svg"},f222:function(i,c,t){i.exports=t.p+"img/check.svg"}}]);