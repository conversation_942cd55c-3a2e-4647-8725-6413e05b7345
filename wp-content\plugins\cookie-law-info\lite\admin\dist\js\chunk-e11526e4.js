(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e11526e4"],{"030a":function(t,e,o){},"03b4":function(t,e,o){"use strict";o.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"cky-consent-chart-section"},[e("cky-card",{attrs:{title:t.$i18n.__("Consent trends","cookie-law-info"),subtitle:t.$i18n.__("(Last 7 days)","cookie-law-info"),loading:t.cardLoader},scopedSlots:t._u([{key:"body",fn:function(){return[t.consentLogData.length?e("div",{staticClass:"cky-consent-chart"},[t.loaded?e("apexcharts",{attrs:{height:"100%",type:"donut",series:t.consentLogData,options:t.chartOptions}}):t._e()],1):e("cky-empty",{attrs:{emptyMessage:"No consents were logged",width:"80px",height:"80px"}})]},proxy:!0},{key:"footer",fn:function(){return[e("div",{staticClass:"cky-card-row"},[e("div",{staticClass:"cky-card-row-actions"},[e("a",{staticClass:"cky-button cky-button-outline cky-external-link cky-button-medium",on:{click:function(e){return t.$router.redirectToApp("website-reports")}}},[t._v(t._s(t.$i18n.__("View All","cookie-law-info"))+" ")])])])]},proxy:!0}])})],1)},a=[],s=o("1321"),i=o.n(s),c=o("9610"),r=o("3337"),l=o("f9c4"),d={components:{apexcharts:i.a,CkyCard:c["a"],CkyEmpty:r["a"]},created(){this.getChartData()},data(){return{loading:!1,loaded:!1,consentLogData:[],chartOptions:{chart:{type:"donut"},colors:["rgba(51, 168, 129, 0.5)","rgba(236, 74, 94, 0.5)","rgba(68, 147, 249, 0.5)"],labels:["Accepted","Rejected","Partially Accepted"],tooltip:{enabled:!0,fillSeriesColor:!1,custom:function({series:t,seriesIndex:e,w:o}){let n;switch(e){case 0:n="#33A881";break;case 1:n="#EC4A5E";break;default:n="#4493F9"}let a=0;for(let i of t)a+=i;const s=t[e];return`<div class="cky-chart-tooltip">\n\t\t\t\t\t\t<div style="color: ${n};">${(s/a*100).toFixed(0)}%</div>\n\t\t\t\t\t\t<div>${o.config.labels[e]}: ${s}</div>\n\t\t\t\t\t\t</div>`}},dataLabels:{enabled:!1},plotOptions:{pie:{expandOnClick:!1,donut:{size:"80%",labels:{show:"always",fontSize:"12px",name:{fontSize:"12px",fontWeight:"400",color:"#4E4B66",offsetY:20},value:{fontSize:"32px",color:"#4E4B66",fontWeight:"700",offsetY:-30},total:{show:!0,label:"Total Consents",color:"#4E4B66",fontSize:"12px",showAlways:!0}}}}},stroke:{width:0},legend:{position:"right",offsetY:-20,markers:{size:6,offsetX:-8,shape:"square"},itemMargin:{vertical:5},onItemClick:{toggleDataSeries:!1},onItemHover:{highlightDataSeries:!1}},states:{active:{filter:{type:"none"}}}}}},methods:{async getChartData(){this.loading=!0;let t=[];try{if(await l["a"].get({path:"consent_logs/statistics"}).then(e=>{t=e}),t.length<=0)return void(this.loading=!1);let e=[this.getCount(t,"accepted"),this.getCount(t,"rejected"),this.getCount(t,"partial")];e=e.filter(t=>0!==t),e&&e.length>0&&(this.consentLogData=e)}catch(e){console.error(e)}this.loading=!1,this.loaded=!0},getCount(t,e){let o=!1;return"object"===typeof t&&(o=t.find((function(t){return t.type===e}))),o&&o.count||0}},computed:{cardLoader(){return!this.$store.state.settings.info||this.loading}}},h=d,u=(o("706c"),o("2877")),p=Object(u["a"])(h,n,a,!1,null,null,null);e["default"]=p.exports},"706c":function(t,e,o){"use strict";o("030a")}}]);