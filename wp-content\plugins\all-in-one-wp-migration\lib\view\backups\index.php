<?php
/**
 * Copyright (C) 2014-2025 ServMask Inc.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 *
 * Attribution: This code is part of the All-in-One WP Migration plugin, developed by
 *
 * ███████╗███████╗██████╗ ██╗   ██╗███╗   ███╗ █████╗ ███████╗██╗  ██╗
 * ██╔════╝██╔════╝██╔══██╗██║   ██║████╗ ████║██╔══██╗██╔════╝██║ ██╔╝
 * ███████╗█████╗  ██████╔╝██║   ██║██╔████╔██║███████║███████╗█████╔╝
 * ╚════██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██║╚██╔╝██║██╔══██║╚════██║██╔═██╗
 * ███████║███████╗██║  ██║ ╚████╔╝ ██║ ╚═╝ ██║██║  ██║███████║██║  ██╗
 * ╚══════╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚═╝     ╚═╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝
 */

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Kangaroos cannot jump here' );
}
?>

<div class="ai1wm-container">
	<div class="ai1wm-row">
		<div class="ai1wm-left">
			<div class="ai1wm-holder">
				<h1>
					<i class="ai1wm-icon-export"></i>
					<?php esc_html_e( 'Backups', 'all-in-one-wp-migration' ); ?>
				</h1>

				<?php if ( is_readable( AI1WM_BACKUPS_PATH ) && is_writable( AI1WM_BACKUPS_PATH ) ) : ?>
					<div id="ai1wm-backups-list">
						<?php include AI1WM_TEMPLATES_PATH . '/backups/backups-list.php'; ?>
					</div>

					<form action="" method="post" id="ai1wm-export-form" class="ai1wm-clear">
						<div id="ai1wm-backups-create">
							<p class="ai1wm-backups-empty-spinner-holder ai1wm-hide">
								<span class="spinner"></span>
								<?php esc_html_e( 'Refreshing backup list...', 'all-in-one-wp-migration' ); ?>
							</p>
							<p class="ai1wm-backups-empty <?php echo empty( $backups ) ? null : 'ai1wm-hide'; ?>">
								<?php esc_html_e( 'No backups found. Create a new one?', 'all-in-one-wp-migration' ); ?>
							</p>
							<p>
								<a href="#" id="ai1wm-create-backup" class="ai1wm-button-green">
									<i class="ai1wm-icon-export"></i>
									<?php esc_html_e( 'Create backup', 'all-in-one-wp-migration' ); ?>
								</a>
							</p>
						</div>
						<input type="hidden" name="ai1wm_manual_export" value="1" />
					</form>

					<?php do_action( 'ai1wm_backups_left_end' ); ?>

				<?php else : ?>

					<?php include AI1WM_TEMPLATES_PATH . '/backups/backups-permissions.php'; ?>

				<?php endif; ?>
			</div>

			<div id="ai1wm-backups-list-archive-browser">
				<archive-browser></archive-browser>
			</div>

		</div>

		<?php include AI1WM_TEMPLATES_PATH . '/common/sidebar-right.php'; ?>

	</div>
</div>
