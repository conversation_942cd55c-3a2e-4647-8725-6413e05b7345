(()=>{var e={31:(e,t,a)=>{var o=a(456),i=jQuery,n=function(){var e=this;this.params=[],this.modal=new o,this.modal.onStop=function(t){e.onStop(t)}};n.prototype.setParams=function(e){this.params=Ai1wm.Util.list(e)},n.prototype.start=function(e,t){var a=this;if(0===(t=t||0)&&this.stopExport(!1),!this.isExportStopped()){i(window).bind("beforeunload",(function(){return ai1wm_locale.stop_exporting_your_website})),this.setStatus({type:"info",message:ai1wm_locale.preparing_to_export});var o=this.params.concat({name:"secret_key",value:ai1wm_export.secret_key});e&&(o=o.concat(Ai1wm.Util.list(e))),i.ajax({url:ai1wm_export.ajax.url,type:"POST",dataType:"json",data:o,dataFilter:function(e){return Ai1wm.Util.json(e)}}).done((function(){a.getStatus()})).done((function(e){e&&a.run(e)})).fail((function(i){var n=1e3*t;try{var r=Ai1wm.Util.json(i.responseText);if(r){var s=JSON.parse(r).errors.pop();if(s.message)return a.stopExport(!0),void a.setStatus({type:"error",title:ai1wm_locale.unable_to_export,message:s.message,nonce:Ai1wm.Util.findValueByName(o,"storage")})}}catch(e){}if(t>=5)return a.stopExport(!0),void a.setStatus({type:"error",title:ai1wm_locale.unable_to_export,message:ai1wm_locale.unable_to_start_the_export,nonce:Ai1wm.Util.findValueByName(o,"storage")});t++,setTimeout(a.start.bind(a,e,t),n)}))}},n.prototype.run=function(e,t){var a=this;t=t||0,this.isExportStopped()||i.ajax({url:ai1wm_export.ajax.url,type:"POST",dataType:"json",data:e,dataFilter:function(e){return Ai1wm.Util.json(e)}}).done((function(e){e&&a.run(e)})).fail((function(o){var i=1e3*t;try{var n=Ai1wm.Util.json(o.responseText);if(n){var r=JSON.parse(n).errors.pop();if(r.message)return a.stopExport(!0),void a.setStatus({type:"error",title:ai1wm_locale.unable_to_export,message:r.message,nonce:Ai1wm.Util.findValueByName(e,"storage")})}}catch(e){}if(t>=5)return a.stopExport(!0),void a.setStatus({type:"error",title:ai1wm_locale.unable_to_export,message:ai1wm_locale.unable_to_run_the_export,nonce:Ai1wm.Util.findValueByName(e,"storage")});t++,setTimeout(a.run.bind(a,e,t),i)}))},n.prototype.clean=function(e,t){var a=this;0===(t=t||0)&&this.stopExport(!0),this.setStatus({type:"info",message:ai1wm_locale.please_wait_stopping_the_export});var o=this.params.concat({name:"secret_key",value:ai1wm_export.secret_key}).concat({name:"priority",value:300}).concat({name:"ai1wm_export_cancel",value:1});e&&(o=o.concat(Ai1wm.Util.list(e))),i.ajax({url:ai1wm_export.ajax.url,type:"POST",dataType:"json",data:o,dataFilter:function(e){return Ai1wm.Util.json(e)}}).done((function(){i(window).unbind("beforeunload"),a.modal.destroy()})).fail((function(i){var n=1e3*t;try{var r=Ai1wm.Util.json(i.responseText);if(r){var s=JSON.parse(r).errors.pop();if(s.message)return a.stopExport(!0),void a.setStatus({type:"error",title:ai1wm_locale.unable_to_export,message:s.message,nonce:Ai1wm.Util.findValueByName(o,"storage")})}}catch(e){}if(t>=5)return a.stopExport(!0),void a.setStatus({type:"error",title:ai1wm_locale.unable_to_export,message:ai1wm_locale.unable_to_stop_the_export,nonce:Ai1wm.Util.findValueByName(o,"storage")});t++,setTimeout(a.clean.bind(a,e,t),n)}))},n.prototype.getStatus=function(){var e=this;this.isExportStopped()||(this.statusXhr=i.ajax({url:ai1wm_export.status.url,type:"GET",dataType:"json",cache:!1,dataFilter:function(e){return Ai1wm.Util.json(e)}}).done((function(t){if(t)switch(e.setStatus(t),t.type){case"done":case"error":case"download":return void i(window).unbind("beforeunload")}setTimeout(e.getStatus.bind(e),3e3)})).fail((function(){setTimeout(e.getStatus.bind(e),3e3)})))},n.prototype.setStatus=function(e){this.modal.render(e)},n.prototype.onStop=function(e){this.clean(e)},n.prototype.stopExport=function(e){try{e&&this.statusXhr&&this.statusXhr.abort()}finally{this.isStopped=e}},n.prototype.isExportStopped=function(){return this.isStopped},e.exports=n},456:e=>{var t=jQuery,a=function(){var e=this;this.error=function(a){var o=t("<div></div>"),i=t("<section></section>"),n=t("<h1></h1>"),r=t("<p></p>").html(a.message),s=t("<div></div>"),p=t("<span></span>").addClass("ai1wm-title-red").text(a.title),c=t('<button type="button" class="ai1wm-button-red"></button>').on("click",(function(){e.destroy()}));if(c.append(ai1wm_locale.close_export),s.append(c),n.append(p),i.append(n).append(r),a.nonce){var l=t('<a target="_blank"></a>');l.text(ai1wm_locale.view_error_log_button),l.prop("href",ai1wm_export.storage.url+"/"+ai1wm_export.error_log.pattern.replace("%s",a.nonce)),i.append(t("<div></div>").append(l))}o.append(i).append(s),e.modal.html(o).show(),e.modal.trigger("focus"),e.overlay.show()},this.info=function(a){var o=t("<div></div>"),i=t("<section></section>"),n=t("<h1></h1>"),r=t("<p></p>").html(a.message),s=t("<div></div>"),p=t('<span class="ai1wm-loader"></span>'),c=t('<button type="button" class="ai1wm-button-red"></button>').on("click",(function(){c.attr("disabled","disabled"),e.onStop()}));c.append('<i class="ai1wm-icon-notification"></i> '+ai1wm_locale.stop_export),s.append(c),n.append(p),i.append(n).append(r),o.append(i).append(s),e.modal.html(o).show(),e.modal.trigger("focus"),e.overlay.show()},this.done=function(a){var o=t("<div></div>"),i=t("<section></section>"),n=t("<h1></h1>"),r=t("<p></p>").html(a.message),s=t("<div></div>"),p=t("<span></span>").addClass("ai1wm-title-green").text(a.title),c=t('<button type="button" class="ai1wm-button-red"></button>').on("click",(function(){e.destroy()}));c.append(ai1wm_locale.close_export),s.append(c),n.append(p),i.append(n).append(r),o.append(i).append(s),e.modal.html(o).show(),e.modal.trigger("focus"),e.overlay.show()},this.download=function(a){var o=t("<div></div>"),i=t("<section></section>"),n=t("<p></p>").html(a.message),r=t("<div></div>"),s=t('<button type="button" class="ai1wm-button-red"></button>').on("click",(function(){e.destroy()})),p=t(".ai1wm-menu-count");p.text(+p.text()+1),p.text()>1?p.prop("title",ai1wm_locale.backups_count_plural.replace("%d",p.text())):(p.removeClass("ai1wm-menu-hide"),p.prop("title",ai1wm_locale.backups_count_singular.replace("%d",p.text()))),s.append(ai1wm_locale.close_export),r.append(s),i.append(n),o.append(i).append(r),e.modal.html(o).show(),e.modal.trigger("focus"),e.overlay.show()},this.overlay=t('<div class="ai1wm-overlay"></div>'),this.modal=t('<div class="ai1wm-modal-container" role="dialog" tabindex="-1"></div>'),t("body").append(this.overlay).append(this.modal)};a.prototype.render=function(e){switch(t(document).trigger("ai1wm-export-status",e),e.type){case"error":this.error(e);break;case"info":this.info(e);break;case"done":this.done(e);break;case"download":this.download(e)}},a.prototype.destroy=function(){this.modal.hide(),this.overlay.hide()},e.exports=a},647:()=>{var e;(e=jQuery).fn.ai1wm_find_replace=function(){return e(this).on("click",(function(t){t.preventDefault();var a=e("#ai1wm-queries > li:first").clone();a.find("input").val(""),a.find(".ai1wm-query-find-text").html("&lt;text&gt;"),a.find(".ai1wm-query-replace-text").html("&lt;another-text&gt;"),e("#ai1wm-queries > li").removeClass("ai1wm-open"),e(a).addClass("ai1wm-open"),e("#ai1wm-queries").append(a),e(a).ai1wm_query(),e(a).find("p:first").on("click",(function(){e(this).parent().toggleClass("ai1wm-open")}))})),this}},705:()=>{var e;(e=jQuery).fn.ai1wm_query=function(){var t=e(this).find("input.ai1wm-query-find-input"),a=e(this).find("input.ai1wm-query-replace-input"),o=e(this).find("small.ai1wm-query-find-text"),i=e(this).find("small.ai1wm-query-replace-text");return t.on("change paste input keypress keydown keyup",(function(){var t=e(this).val().length>0?e(this).val():"<text>";o.text(t)})),a.on("change paste input keypress keydown keyup",(function(){var t=e(this).val().length>0?e(this).val():"<another-text>";i.text(t)})),this}},892:()=>{jQuery(document).ready((function(e){"use strict";e("#ai1wm-feedback-type-link-1").on("click",(function(){var t=e("#ai1wm-feedback-type-1");t.is(":checked")?t.attr("checked",!1):t.attr("checked",!0)})),e("#ai1wm-feedback-type-2").on("click",(function(){e("#ai1wm-feedback-type-1").closest("li").hide(),e(".ai1wm-feedback-form").fadeIn()})),e("#ai1wm-feedback-cancel").on("click",(function(t){e(".ai1wm-feedback-form").fadeOut((function(){e(".ai1wm-feedback-type").attr("checked",!1).closest("li").show()})),t.preventDefault()})),e("#ai1wm-feedback-submit").on("click",(function(t){var a=e(this),o=a.next(),i=e(".ai1wm-feedback-type:checked").val(),n=e(".ai1wm-feedback-email").val(),r=e(".ai1wm-feedback-message").val(),s=e(".ai1wm-feedback-terms").is(":checked");a.attr("disabled",!0),o.css("visibility","visible"),e.ajax({url:ai1wm_feedback.ajax.url,type:"POST",dataType:"json",async:!0,data:{secret_key:ai1wm_feedback.secret_key,ai1wm_type:i,ai1wm_email:n,ai1wm_message:r,ai1wm_terms:+s},dataFilter:function(e){return Ai1wm.Util.json(e)}}).done((function(t){if(a.attr("disabled",!1),o.css("visibility","hidden"),t.errors.length>0){e(".ai1wm-feedback .ai1wm-message").remove();var i=e("<div />").addClass("ai1wm-message ai1wm-error-message");e.each(t.errors,(function(t,a){i.append(e("<p />").text(a))})),e(".ai1wm-feedback").prepend(i)}else{var n=e("<div />").addClass("ai1wm-message ai1wm-success-message");n.append(e("<p />").text(ai1wm_locale.thanks_for_submitting_your_feedback)),e(".ai1wm-feedback").html(n)}})),t.preventDefault()}))}))},213:function(e,t,a){var o,i,n;i=[],void 0===(n="function"==typeof(o=function(){"use strict";function t(e,t){return void 0===t?t={autoBom:!1}:"object"!=typeof t&&(console.warn("Deprecated: Expected third argument to be a object"),t={autoBom:!t}),t.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob(["\ufeff",e],{type:e.type}):e}function o(e,t,a){var o=new XMLHttpRequest;o.open("GET",e),o.responseType="blob",o.onload=function(){p(o.response,t,a)},o.onerror=function(){console.error("could not download file")},o.send()}function i(e){var t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch(e){}return 200<=t.status&&299>=t.status}function n(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(a){var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(t)}}var r="object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof a.g&&a.g.global===a.g?a.g:void 0,s=r.navigator&&/Macintosh/.test(navigator.userAgent)&&/AppleWebKit/.test(navigator.userAgent)&&!/Safari/.test(navigator.userAgent),p=r.saveAs||("object"!=typeof window||window!==r?function(){}:"download"in HTMLAnchorElement.prototype&&!s?function(e,t,a){var s=r.URL||r.webkitURL,p=document.createElement("a");t=t||e.name||"download",p.download=t,p.rel="noopener","string"==typeof e?(p.href=e,p.origin===location.origin?n(p):i(p.href)?o(e,t,a):n(p,p.target="_blank")):(p.href=s.createObjectURL(e),setTimeout((function(){s.revokeObjectURL(p.href)}),4e4),setTimeout((function(){n(p)}),0))}:"msSaveOrOpenBlob"in navigator?function(e,a,r){if(a=a||e.name||"download","string"!=typeof e)navigator.msSaveOrOpenBlob(t(e,r),a);else if(i(e))o(e,a,r);else{var s=document.createElement("a");s.href=e,s.target="_blank",setTimeout((function(){n(s)}))}}:function(e,t,a,i){if((i=i||open("","_blank"))&&(i.document.title=i.document.body.innerText="downloading..."),"string"==typeof e)return o(e,t,a);var n="application/octet-stream"===e.type,p=/constructor/i.test(r.HTMLElement)||r.safari,c=/CriOS\/[\d]+/.test(navigator.userAgent);if((c||n&&p||s)&&"undefined"!=typeof FileReader){var l=new FileReader;l.onloadend=function(){var e=l.result;e=c?e:e.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=e:location=e,i=null},l.readAsDataURL(e)}else{var d=r.URL||r.webkitURL,u=d.createObjectURL(e);i?i.location=u:location.href=u,i=null,setTimeout((function(){d.revokeObjectURL(u)}),4e4)}});r.saveAs=p.saveAs=p,e.exports=p})?o.apply(t,i):o)||(e.exports=n)}},t={};function a(o){var i=t[o];if(void 0!==i)return i.exports;var n=t[o]={exports:{}};return e[o].call(n.exports,n,n.exports,a),n.exports}a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var o in t)a.o(t,o)&&!a.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},a.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=a(213),t=a(705),o=a(647),i=a(892),n=a(31);jQuery(document).ready((function(t){var a=new n;t("#ai1wm-export-file").on("click",(function(e){if(t("#ai1wm-encrypt-backups").is(":checked")){var o=t("#ai1wm-backup-encrypt-password"),i=t("#ai1wm-backup-encrypt-password-confirmation");if(!o.val().length)return o.parent().addClass("ai1wm-has-error"),o.focus(),!1;if(o.val()!==i.val())return i.parent().addClass("ai1wm-has-error"),i.focus(),!1}var n=Ai1wm.Util.random(12),r=Ai1wm.Util.form("#ai1wm-export-form").concat({name:"storage",value:n}).concat({name:"file",value:1});a.setParams(r),a.start(),e.preventDefault()})),t(document).on("click",".ai1wm-modal-container .ai1wm-direct-download",(function(a){a.preventDefault();var o=t(this).prop("download"),i={secret_key:ai1wm_export.secret_key,archive:o},n=new XMLHttpRequest;n.addEventListener("readystatechange",(function(){2===n.readyState&&200===n.status||3===n.readyState||4===n.readyState&&(n.status<400?(0,e.saveAs)(n.response,Ai1wm.Util.basename(o)):alert(ai1wm_locale.archive_browser_download_error))})),n.responseType="blob";var r=new FormData;for(var s in i)r.append(s,i[s]);n.open("post",ai1wm_export.download.url),n.send(r)})),t(".ai1wm-accordion > .ai1wm-title").on("click",(function(){t(this).parent().toggleClass("ai1wm-active")})),t("#ai1wm-add-new-replace-button").ai1wm_find_replace(),t(".ai1wm-expandable > p:first, .ai1wm-expandable > h4:first, .ai1wm-expandable > div.ai1wm-button-main").on("click",(function(){t(this).parent().toggleClass("ai1wm-open")})),t(".ai1wm-query").ai1wm_query(),t(".ai1wm-toggle-password-visibility").on("click",(function(){return t(this).toggleClass("ai1wm-icon-eye ai1wm-icon-eye-blocked"),t(this).prev().prop("type",(function(e,t){return"text"===t?"password":"text"})),!1})),t("#ai1wm-encrypt-backups").on("click",(function(){t(".ai1wm-encrypt-backups-passwords-toggle").toggle()})),t("#ai1wm-backup-encrypt-password").on("keyup",(function(){var e=t(this),a=t("#ai1wm-backup-encrypt-password-confirmation");e.val()!==a.val()&&a.parent().addClass("ai1wm-has-error"),e.val().length&&e.parent().removeClass("ai1wm-has-error")})),t("#ai1wm-backup-encrypt-password-confirmation").on("keyup",(function(){var e=t(this);t("#ai1wm-backup-encrypt-password").val()!==e.val()?e.parent().addClass("ai1wm-has-error"):e.parent().removeClass("ai1wm-has-error")}))})),a.g.Ai1wm=jQuery.extend({},a.g.Ai1wm,{Query:t,FindReplace:o,Feedback:i,Export:n})})()})();