(()=>{var e={892:()=>{jQuery(document).ready((function(e){"use strict";e("#ai1wm-feedback-type-link-1").on("click",(function(){var a=e("#ai1wm-feedback-type-1");a.is(":checked")?a.attr("checked",!1):a.attr("checked",!0)})),e("#ai1wm-feedback-type-2").on("click",(function(){e("#ai1wm-feedback-type-1").closest("li").hide(),e(".ai1wm-feedback-form").fadeIn()})),e("#ai1wm-feedback-cancel").on("click",(function(a){e(".ai1wm-feedback-form").fadeOut((function(){e(".ai1wm-feedback-type").attr("checked",!1).closest("li").show()})),a.preventDefault()})),e("#ai1wm-feedback-submit").on("click",(function(a){var i=e(this),t=i.next(),c=e(".ai1wm-feedback-type:checked").val(),r=e(".ai1wm-feedback-email").val(),s=e(".ai1wm-feedback-message").val(),n=e(".ai1wm-feedback-terms").is(":checked");i.attr("disabled",!0),t.css("visibility","visible"),e.ajax({url:ai1wm_feedback.ajax.url,type:"POST",dataType:"json",async:!0,data:{secret_key:ai1wm_feedback.secret_key,ai1wm_type:c,ai1wm_email:r,ai1wm_message:s,ai1wm_terms:+n},dataFilter:function(e){return Ai1wm.Util.json(e)}}).done((function(a){if(i.attr("disabled",!1),t.css("visibility","hidden"),a.errors.length>0){e(".ai1wm-feedback .ai1wm-message").remove();var c=e("<div />").addClass("ai1wm-message ai1wm-error-message");e.each(a.errors,(function(a,i){c.append(e("<p />").text(i))})),e(".ai1wm-feedback").prepend(c)}else{var r=e("<div />").addClass("ai1wm-message ai1wm-success-message");r.append(e("<p />").text(ai1wm_locale.thanks_for_submitting_your_feedback)),e(".ai1wm-feedback").html(r)}})),a.preventDefault()}))}))}},a={};function i(t){var c=a[t];if(void 0!==c)return c.exports;var r=a[t]={exports:{}};return e[t](r,r.exports,i),r.exports}i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}();var t=i(892);jQuery(document).ready((function(){})),i.g.Ai1wm=jQuery.extend({},i.g.Ai1wm,{Feedback:t})})();