/* ============================================================
 * Collab Popup Component
 * Manages the state and behavior of collaboration popup
 * ============================================================ */

const CollabPopup = (function($) {
    'use strict';

    // Constants
    const STORAGE_KEY = 'collab-popup-state';
    const STATES = {
        MAXIMIZE: 'maximize',
        MINIMIZE: 'minimize'
    };
    const SELECTORS = {
        TOGGLE_BUTTON: '.collab__toggle',
        SHORT_POPUP: '.collab-short',
        FULL_POPUP: '.collab-full'
    };
    const EVENTS = {
        POPUP_TOGGLE: 'collab-popup-toggle'
    };
    const CSS_CLASSES = {
        HIDE: 'hide',
        SHOW: 'show'
    };

    /**
     * Get the current popup state from localStorage
     * @returns {string|null} Current state or null if not set
     */
    function getPopupState() {
        return localStorage.getItem(STORAGE_KEY);
    }

    /**
     * Set the popup state in localStorage
     * @param {string} state - The state to save
     */
    function setPopupState(state) {
        localStorage.setItem(STORAGE_KEY, state);
    }

    /**
     * Toggle the popup state between maximize and minimize
     */
    function togglePopupState() {
        const currentState = getPopupState();
        const newState = currentState === STATES.MAXIMIZE ? STATES.MINIMIZE : STATES.MAXIMIZE;

        setPopupState(newState);
        
        // Trigger custom event with the previous state
        $(document).trigger(EVENTS.POPUP_TOGGLE, [newState]);
    }

    /**
     * Show the full popup and hide the short version
     */
    function showFullPopup() {
        $(SELECTORS.SHORT_POPUP).addClass(CSS_CLASSES.HIDE);
        $(SELECTORS.FULL_POPUP).addClass(CSS_CLASSES.SHOW);
    }

    /**
     * Show the short popup and hide the full version
     */
    function showShortPopup() {
        $(SELECTORS.FULL_POPUP).removeClass(CSS_CLASSES.SHOW);
        $(SELECTORS.SHORT_POPUP).removeClass(CSS_CLASSES.HIDE);
    }

    /**
     * Handle popup state changes
     * @param {string} newState - The state before the toggle
     */
    function handleStateChange(newState) {
        if (newState === STATES.MAXIMIZE) {
            showFullPopup();
        } else {
            showShortPopup();
        }
    }

    /**
     * Initialize the popup with default state if none exists
     */
    function initializeDefaultState() {
        const currentState = getPopupState();
        if (!currentState) {
            showFullPopup();
            setPopupState(STATES.MAXIMIZE);
        } else {
            setPopupState(STATES.MINIMIZE);
        }
    }

    /**
     * Bind event handlers
     */
    function bindEvents() {
        // Handle toggle button clicks
        $(document).on('click', SELECTORS.TOGGLE_BUTTON, togglePopupState);
        
        // Handle custom popup toggle events
        $(document).on(EVENTS.POPUP_TOGGLE, function(event, newState) {
            handleStateChange(newState);
        });
    }

    /**
     * Initialize the collab popup component
     */
    function init() {
        bindEvents();
        initializeDefaultState();
    }

    // Public API
    return {
        init: init,
        toggle: togglePopupState,
        getState: getPopupState,
        setState: setPopupState
    };

})(jQuery);

// Initialize when DOM is ready
jQuery(document).ready(function() {
    CollabPopup.init();
});
